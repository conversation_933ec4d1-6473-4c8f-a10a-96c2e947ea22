"""
Test the AI parser integration
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_parser import AISchemeParser

def test_ai_parser():
    """Test the AI parser with a simple scheme"""
    print("Testing AI-powered scheme parser...")
    
    # Test content
    test_content = """
    GRADE 9 PRE-TECHNICAL STUDIES SCHEME OF WORK - TERM 2
    
    WEEK 1: INTRODUCTION TO MATERIALS
    Strand: Materials and Their Properties
    Sub-strand: Classification of Materials
    Learning Outcomes: By the end of the lesson, learners should be able to:
    - Identify different types of materials
    - Classify materials according to their properties
    - Explain the importance of material selection
    
    Learning Experiences:
    - Observe and handle different materials
    - Discuss properties of various materials
    - Group materials based on characteristics
    
    Key Inquiry Question: How do we choose the right material for specific purposes?
    
    Resources: Various material samples, charts, worksheets
    Assessment: Practical identification of materials
    
    WEEK 2: MATERIAL PROPERTIES
    Strand: Materials and Their Properties  
    Sub-strand: Physical Properties
    Learning Outcomes: Students will understand physical properties of materials
    Learning Experiences: Testing material properties through experiments
    """
    
    try:
        parser = AISchemeParser()
        print("AI Parser created successfully")
        
        # Parse the content
        print("Parsing content with AI...")
        result = parser.parse_scheme_with_ai(test_content, "test_scheme.txt")
        
        if result['success']:
            print(f"✅ AI Parsing successful!")
            print(f"Message: {result['message']}")
            print(f"Weeks found: {result['weeks_found']}")
            print(f"Lesson plans generated: {len(result['lesson_plans'])}")
            
            # Show first lesson plan structure
            if result['lesson_plans']:
                first_plan = result['lesson_plans'][0]
                print(f"\nFirst lesson plan preview:")
                print(f"- Week: {first_plan.get('week')}")
                print(f"- Strand: {first_plan.get('strand')}")
                print(f"- Sub-strand: {first_plan.get('sub_strand')}")
                print(f"- Learning outcomes: {len(first_plan.get('specific_learning_outcomes', []))}")
                print(f"- Learning experiences: {len(first_plan.get('learning_experiences', []))}")
        else:
            print(f"❌ AI Parsing failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_parser()
