# CBC Lesson Plan Generator

This project is a web application that helps teachers generate CBC-compliant lesson plans from a scheme of work.

## How to Use

1.  **Upload or Paste:** Upload your scheme of work file (PDF, Word, etc.) or paste the content directly.
2.  **Configure:** Fill in details like school, grade, subject, and start date.
3.  **Generate:** The tool will parse the scheme and generate detailed lesson plans.
4.  **Download:** You can download the generated lesson plans as text files.

## Technologies Used

-   **Vite**
-   **TypeScript**
-   **React**
-   **shadcn/ui**
-   **Tailwind CSS**

## Local Development

To run this project locally, you need Node.js and npm installed.

```sh
# 1. Clone the repository
git clone <YOUR_GIT_URL>

# 2. Navigate to the project directory
cd <YOUR_PROJECT_NAME>

# 3. Install dependencies
npm i

# 4. Start the development server
npm run dev
```