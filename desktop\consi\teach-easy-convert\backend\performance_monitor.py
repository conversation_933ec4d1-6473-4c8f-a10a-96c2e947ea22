"""
Performance Monitoring System for CBC Lesson Plan Generator
Tracks processing times and identifies bottlenecks
"""
import time
import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
from functools import wraps
import psutil
import threading

class PerformanceMonitor:
    """
    Comprehensive performance monitoring for the CBC system
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_processing_time": 0,
            "slowest_request": 0,
            "fastest_request": float('inf'),
            "bottlenecks": {},
            "component_times": {},
            "memory_usage": [],
            "cpu_usage": []
        }
        self.request_history = []
        self.active_requests = {}
        self._monitoring = False
        
    def start_monitoring(self):
        """Start system resource monitoring"""
        self._monitoring = True
        threading.Thread(target=self._monitor_resources, daemon=True).start()
        
    def stop_monitoring(self):
        """Stop system resource monitoring"""
        self._monitoring = False
        
    def _monitor_resources(self):
        """Monitor system resources in background"""
        while self._monitoring:
            try:
                memory_percent = psutil.virtual_memory().percent
                cpu_percent = psutil.cpu_percent(interval=1)
                
                self.metrics["memory_usage"].append({
                    "timestamp": datetime.now().isoformat(),
                    "memory_percent": memory_percent
                })
                
                self.metrics["cpu_usage"].append({
                    "timestamp": datetime.now().isoformat(),
                    "cpu_percent": cpu_percent
                })
                
                # Keep only last 100 readings
                if len(self.metrics["memory_usage"]) > 100:
                    self.metrics["memory_usage"] = self.metrics["memory_usage"][-100:]
                if len(self.metrics["cpu_usage"]) > 100:
                    self.metrics["cpu_usage"] = self.metrics["cpu_usage"][-100:]
                    
                time.sleep(5)  # Monitor every 5 seconds
            except Exception as e:
                self.logger.error(f"Resource monitoring error: {e}")
                
    def start_request(self, request_id: str, operation: str, details: Dict = None) -> str:
        """Start tracking a request"""
        self.active_requests[request_id] = {
            "operation": operation,
            "start_time": time.time(),
            "details": details or {},
            "components": {},
            "status": "active"
        }
        return request_id
        
    def end_request(self, request_id: str, success: bool = True, result_size: int = 0):
        """End tracking a request"""
        if request_id not in self.active_requests:
            return
            
        request_data = self.active_requests[request_id]
        end_time = time.time()
        total_time = end_time - request_data["start_time"]
        
        # Update metrics
        self.metrics["total_requests"] += 1
        if success:
            self.metrics["successful_requests"] += 1
        else:
            self.metrics["failed_requests"] += 1
            
        # Update timing metrics
        if total_time > self.metrics["slowest_request"]:
            self.metrics["slowest_request"] = total_time
        if total_time < self.metrics["fastest_request"]:
            self.metrics["fastest_request"] = total_time
            
        # Calculate average
        current_avg = self.metrics["average_processing_time"]
        total_requests = self.metrics["total_requests"]
        self.metrics["average_processing_time"] = (
            (current_avg * (total_requests - 1) + total_time) / total_requests
        )
        
        # Store request history
        request_summary = {
            "request_id": request_id,
            "operation": request_data["operation"],
            "total_time": total_time,
            "success": success,
            "result_size": result_size,
            "components": request_data["components"],
            "timestamp": datetime.now().isoformat(),
            "details": request_data["details"]
        }
        
        self.request_history.append(request_summary)
        
        # Keep only last 50 requests
        if len(self.request_history) > 50:
            self.request_history = self.request_history[-50:]
            
        # Identify bottlenecks
        self._identify_bottlenecks(request_summary)
        
        # Clean up
        del self.active_requests[request_id]
        
    def track_component(self, request_id: str, component: str, duration: float, details: Dict = None):
        """Track individual component performance"""
        if request_id not in self.active_requests:
            return
            
        self.active_requests[request_id]["components"][component] = {
            "duration": duration,
            "details": details or {}
        }
        
        # Update component averages
        if component not in self.metrics["component_times"]:
            self.metrics["component_times"][component] = {
                "total_time": 0,
                "count": 0,
                "average": 0,
                "max": 0,
                "min": float('inf')
            }
            
        comp_metrics = self.metrics["component_times"][component]
        comp_metrics["total_time"] += duration
        comp_metrics["count"] += 1
        comp_metrics["average"] = comp_metrics["total_time"] / comp_metrics["count"]
        
        if duration > comp_metrics["max"]:
            comp_metrics["max"] = duration
        if duration < comp_metrics["min"]:
            comp_metrics["min"] = duration
            
    def _identify_bottlenecks(self, request_summary: Dict):
        """Identify performance bottlenecks"""
        total_time = request_summary["total_time"]
        components = request_summary["components"]
        
        # Flag slow requests (>30 seconds)
        if total_time > 30:
            bottleneck_key = f"slow_request_{request_summary['operation']}"
            if bottleneck_key not in self.metrics["bottlenecks"]:
                self.metrics["bottlenecks"][bottleneck_key] = {
                    "count": 0,
                    "average_time": 0,
                    "description": f"Slow {request_summary['operation']} requests"
                }
            
            bottleneck = self.metrics["bottlenecks"][bottleneck_key]
            bottleneck["count"] += 1
            bottleneck["average_time"] = (
                (bottleneck["average_time"] * (bottleneck["count"] - 1) + total_time) / 
                bottleneck["count"]
            )
            
        # Identify slow components
        for component, data in components.items():
            duration = data["duration"]
            if duration > 10:  # Component taking >10 seconds
                bottleneck_key = f"slow_component_{component}"
                if bottleneck_key not in self.metrics["bottlenecks"]:
                    self.metrics["bottlenecks"][bottleneck_key] = {
                        "count": 0,
                        "average_time": 0,
                        "description": f"Slow {component} component"
                    }
                
                bottleneck = self.metrics["bottlenecks"][bottleneck_key]
                bottleneck["count"] += 1
                bottleneck["average_time"] = (
                    (bottleneck["average_time"] * (bottleneck["count"] - 1) + duration) / 
                    bottleneck["count"]
                )
                
    def get_performance_report(self) -> Dict:
        """Get comprehensive performance report"""
        return {
            "summary": {
                "total_requests": self.metrics["total_requests"],
                "success_rate": (
                    self.metrics["successful_requests"] / max(1, self.metrics["total_requests"]) * 100
                ),
                "average_processing_time": round(self.metrics["average_processing_time"], 2),
                "fastest_request": round(self.metrics["fastest_request"], 2) if self.metrics["fastest_request"] != float('inf') else 0,
                "slowest_request": round(self.metrics["slowest_request"], 2)
            },
            "bottlenecks": self.metrics["bottlenecks"],
            "component_performance": self.metrics["component_times"],
            "recent_requests": self.request_history[-10:],  # Last 10 requests
            "system_resources": {
                "current_memory": psutil.virtual_memory().percent if psutil else "N/A",
                "current_cpu": psutil.cpu_percent() if psutil else "N/A",
                "memory_history": self.metrics["memory_usage"][-10:],
                "cpu_history": self.metrics["cpu_usage"][-10:]
            }
        }
        
    def get_recommendations(self) -> List[str]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        # Check average processing time
        if self.metrics["average_processing_time"] > 60:
            recommendations.append("⚠️ Average processing time is over 1 minute. Consider optimizing AI API calls.")
            
        if self.metrics["average_processing_time"] > 300:
            recommendations.append("🚨 CRITICAL: Average processing time is over 5 minutes. Immediate optimization needed.")
            
        # Check bottlenecks
        for bottleneck_key, bottleneck in self.metrics["bottlenecks"].items():
            if bottleneck["count"] > 3:
                recommendations.append(
                    f"🔍 Frequent bottleneck detected: {bottleneck['description']} "
                    f"(occurred {bottleneck['count']} times, avg: {bottleneck['average_time']:.1f}s)"
                )
                
        # Check component performance
        for component, metrics in self.metrics["component_times"].items():
            if metrics["average"] > 30:
                recommendations.append(
                    f"⏱️ Slow component: {component} averaging {metrics['average']:.1f}s"
                )
                
        # Check success rate
        success_rate = self.metrics["successful_requests"] / max(1, self.metrics["total_requests"]) * 100
        if success_rate < 90:
            recommendations.append(f"❌ Low success rate: {success_rate:.1f}%. Check error handling.")
            
        if not recommendations:
            recommendations.append("✅ System performance looks good!")
            
        return recommendations

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

def monitor_performance(operation: str):
    """Decorator to monitor function performance"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            request_id = f"{operation}_{int(time.time() * 1000)}"
            performance_monitor.start_request(request_id, operation)

            try:
                result = await func(*args, **kwargs)
                result_size = len(str(result)) if result else 0
                performance_monitor.end_request(request_id, True, result_size)
                return result
            except Exception as e:
                performance_monitor.end_request(request_id, False)
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            request_id = f"{operation}_{int(time.time() * 1000)}"
            performance_monitor.start_request(request_id, operation)

            try:
                result = func(*args, **kwargs)
                result_size = len(str(result)) if result else 0
                performance_monitor.end_request(request_id, True, result_size)
                return result
            except Exception as e:
                performance_monitor.end_request(request_id, False)
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

def track_component_time(request_id: str, component: str):
    """Context manager to track component timing"""
    class ComponentTimer:
        def __init__(self, request_id: str, component: str):
            self.request_id = request_id
            self.component = component
            self.start_time = None

        def __enter__(self):
            self.start_time = time.time()
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.start_time:
                duration = time.time() - self.start_time
                performance_monitor.track_component(
                    self.request_id,
                    self.component,
                    duration,
                    {"error": exc_type is not None}
                )

    return ComponentTimer(request_id, component)
