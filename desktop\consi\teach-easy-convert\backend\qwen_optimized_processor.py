"""
Qwen 3.5 Optimized Processor for CBC Lesson Plans
Leverages Qwen 3.5's MoE architecture for ultra-fast document processing
"""

import asyncio
import aiohttp
import json
import logging
import time
from typing import Dict, List, Optional, Tuple

class QwenOptimizedProcessor:
    """
    Specialized processor leveraging Qwen 3.5's Mixture-of-Experts architecture
    for ultra-fast CBC scheme processing (targeting 30-second performance)
    """
    
    def __init__(self):
        self.api_key = "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"
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.logger = logging.getLogger(__name__)
        
        # Qwen 3 MoE model configuration
        self.qwen_model = "qwen/qwen3-235b-a22b:free"  # 235B total, 22B active parameters
        self.max_context_length = 128000  # 128K context window
        self.max_output_tokens = 8000     # Increased for comprehensive lesson plans
        
        # Performance optimization settings
        self.session = None
        self.request_timeout = 60  # Extended for large document processing
        
        # CBC-specific system prompt optimized for Qwen 3.5
        self.cbc_system_prompt = """You are an expert CBC (Competency-Based Curriculum) lesson plan generator for Kenyan schools.

TASK: Convert scheme of work content into detailed lesson plans following CBC standards.

EFFICIENCY MODE: Process the ENTIRE document in ONE request using your 128K context window.

OUTPUT FORMAT (JSON):
{
    "analysis": {
        "total_weeks": number,
        "lessons_per_week": number,
        "total_lessons": number,
        "subject": "string",
        "grade": "string",
        "term": "string"
    },
    "lesson_plans": [
        {
            "week": number,
            "lesson": number,
            "title": "string",
            "strand": "string",
            "sub_strand": "string",
            "specific_learning_outcomes": ["outcome1", "outcome2"],
            "key_inquiry_questions": ["question1", "question2"],
            "learning_experiences": ["activity1", "activity2"],
            "key_vocabulary": ["term1", "term2"],
            "resources": ["resource1", "resource2"],
            "assessment": {
                "formative": "string",
                "summative": "string"
            },
            "differentiation": "string",
            "cross_curricular_links": ["link1", "link2"],
            "values": ["value1", "value2"],
            "non_formal_activities": ["activity1", "activity2"],
            "duration": "40 minutes"
        }
    ]
}

REQUIREMENTS:
- Extract ALL weeks and lessons from the scheme
- Generate comprehensive lesson plans for each lesson
- Follow CBC format exactly
- Include all required CBC components
- Ensure age-appropriate content
- Provide practical learning activities"""

    async def initialize(self):
        """Initialize HTTP session for Qwen API calls"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.request_timeout)
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                keepalive_timeout=30
            )
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={'User-Agent': 'CBC-LessonPlan-Generator/1.0'}
            )

    async def close(self):
        """Clean up HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None

    async def process_scheme_with_qwen(self, content: str, filename: str = "scheme.pdf") -> Dict:
        """
        Process entire scheme document using Qwen 3.5's MoE efficiency
        Target: 30-second processing time like the benchmark
        """
        start_time = time.time()
        
        try:
            await self.initialize()
            
            # Prepare the request for Qwen 3.5
            messages = [
                {"role": "system", "content": self.cbc_system_prompt},
                {"role": "user", "content": f"Process this CBC scheme of work document and generate lesson plans:\n\n{content[:self.max_context_length]}"}
            ]
            
            payload = {
                "model": self.qwen_model,
                "messages": messages,
                "max_tokens": self.max_output_tokens,
                "temperature": 0.1,  # Low temperature for consistent output
                "top_p": 0.9,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "stream": False
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://cbc-lesson-generator.com",
                "X-Title": "CBC Lesson Plan Generator"
            }
            
            self.logger.info(f"🚀 Processing {filename} with Qwen 3.5 MoE architecture...")
            
            async with self.session.post(self.base_url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    if 'choices' in result and len(result['choices']) > 0:
                        content_response = result['choices'][0]['message']['content']
                        
                        # Parse JSON response
                        try:
                            lesson_data = json.loads(content_response)
                            processing_time = time.time() - start_time
                            
                            self.logger.info(f"✅ Qwen 3.5 processing completed in {processing_time:.1f} seconds")
                            
                            # Add metadata
                            lesson_data['metadata'] = {
                                'processing_time': processing_time,
                                'model_used': self.qwen_model,
                                'architecture': 'MoE (235B total, 22B active)',
                                'filename': filename,
                                'timestamp': time.time()
                            }
                            
                            return lesson_data
                            
                        except json.JSONDecodeError as e:
                            self.logger.error(f"Failed to parse Qwen response as JSON: {e}")
                            return self._create_fallback_response(content_response, filename, processing_time)
                    
                    else:
                        self.logger.error("No choices in Qwen API response")
                        return self._create_error_response("No response content", filename)
                
                else:
                    error_text = await response.text()
                    self.logger.error(f"Qwen API error {response.status}: {error_text}")
                    return self._create_error_response(f"API Error {response.status}", filename)
                    
        except asyncio.TimeoutError:
            processing_time = time.time() - start_time
            self.logger.error(f"Qwen processing timeout after {processing_time:.1f} seconds")
            return self._create_error_response("Processing timeout", filename)
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Qwen processing error: {str(e)}")
            return self._create_error_response(str(e), filename)

    def _create_fallback_response(self, raw_content: str, filename: str, processing_time: float) -> Dict:
        """Create fallback response when JSON parsing fails"""
        return {
            "analysis": {
                "total_weeks": 0,
                "lessons_per_week": 0,
                "total_lessons": 0,
                "subject": "Unknown",
                "grade": "Unknown",
                "term": "Unknown"
            },
            "lesson_plans": [],
            "raw_response": raw_content,
            "metadata": {
                "processing_time": processing_time,
                "model_used": self.qwen_model,
                "architecture": "MoE (235B total, 22B active)",
                "filename": filename,
                "status": "fallback_parsing",
                "timestamp": time.time()
            }
        }

    def _create_error_response(self, error_message: str, filename: str) -> Dict:
        """Create error response"""
        return {
            "analysis": {
                "total_weeks": 0,
                "lessons_per_week": 0,
                "total_lessons": 0,
                "subject": "Error",
                "grade": "Error",
                "term": "Error"
            },
            "lesson_plans": [],
            "error": error_message,
            "metadata": {
                "processing_time": 0,
                "model_used": self.qwen_model,
                "architecture": "MoE (235B total, 22B active)",
                "filename": filename,
                "status": "error",
                "timestamp": time.time()
            }
        }

    async def analyze_document_structure(self, content: str) -> Dict:
        """
        Quick analysis of document structure using Qwen 3.5's efficiency
        Similar to the 30-second benchmark analysis
        """
        start_time = time.time()
        
        analysis_prompt = """Analyze this CBC scheme document and provide ONLY the structure count:

REQUIRED OUTPUT (JSON only):
{
    "weeks": number,
    "lessons_per_week": number,
    "total_lessons": number,
    "subject": "string",
    "grade": "string",
    "term": "string"
}

Document content:"""
        
        try:
            await self.initialize()
            
            messages = [
                {"role": "user", "content": f"{analysis_prompt}\n\n{content[:50000]}"}
            ]
            
            payload = {
                "model": self.qwen_model,
                "messages": messages,
                "max_tokens": 500,
                "temperature": 0,
                "stream": False
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            async with self.session.post(self.base_url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    content_response = result['choices'][0]['message']['content']
                    
                    try:
                        analysis = json.loads(content_response)
                        processing_time = time.time() - start_time
                        
                        analysis['processing_time'] = processing_time
                        analysis['model'] = 'Qwen 3.5 MoE'
                        
                        return analysis
                        
                    except json.JSONDecodeError:
                        return {"error": "Failed to parse analysis", "raw": content_response}
                
                else:
                    return {"error": f"API Error {response.status}"}
                    
        except Exception as e:
            return {"error": str(e)}
