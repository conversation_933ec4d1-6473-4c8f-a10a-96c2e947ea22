#!/usr/bin/env python3
"""
Quick analysis of STM2025.pdf to count weeks and lessons
"""
import sys
import os
import re

# Add backend to path
sys.path.append('backend')

from optimized_file_processor import OptimizedFileProcessor

def analyze_stm_pdf():
    """Analyze STM2025.pdf for weeks and lessons"""
    
    # Extract content from PDF
    file_processor = OptimizedFileProcessor()
    
    try:
        with open('STM2025.pdf', 'rb') as f:
            file_content = f.read()
        
        print("📄 Extracting content from STM2025.pdf...")
        content, metadata = file_processor.extract_text_fast(file_content, 'STM2025.pdf', 'analysis')
        
        print(f"✅ Content extracted successfully!")
        print(f"   📊 Total characters: {len(content):,}")
        print(f"   📄 Pages processed: {metadata.get('pages_processed', 0)}")
        print()
        
        # Find all week references
        print("🔍 Analyzing week structure...")
        week_patterns = [
            r'(?i)week\s*(\d+)',
            r'(?i)wk\s*(\d+)',
            r'(\d+)\s*(?:st|nd|rd|th)?\s*week',
            r'(?i)wk\s+(\d+)',
            r'(\d+)\s+wk',
            r'week\s*(\d+)',
            r'(\d+)\s+week'
        ]
        
        weeks_found = set()
        for pattern in week_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if match.isdigit() and 1 <= int(match) <= 20:
                    weeks_found.add(int(match))
        
        weeks_list = sorted(weeks_found)
        print(f"📅 Weeks detected: {weeks_list}")
        print(f"📊 Total weeks: {len(weeks_list)}")
        print()
        
        # Analyze lesson structure
        print("🔍 Analyzing lesson structure...")
        
        # Look for lesson indicators in the content
        lesson_indicators = [
            'specific learning outcomes',
            'key inquiry questions',
            'learning experiences',
            'assessment methods',
            'resources',
            'learning outcome',
            'inquiry question',
            'learning experience',
            'assessment method'
        ]

        # Also look for lesson number patterns
        lesson_patterns = [
            r'(?i)ls\s*n?\s*(\d+)',
            r'(?i)lesson\s*(\d+)',
            r'(?i)l\s*(\d+)',
            r'(\d+)\s*(?:st|nd|rd|th)?\s*lesson'
        ]
        
        lines = content.split('\n')
        lesson_sections = []
        current_week = None
        
        for i, line in enumerate(lines):
            line_clean = line.strip().lower()
            if not line_clean:
                continue
                
            # Track current week - check all patterns
            week_match = None
            for pattern in week_patterns:
                week_match = re.search(pattern, line)
                if week_match:
                    current_week = int(week_match.group(1))
                    break
            
            # Check for lesson structure indicators
            for indicator in lesson_indicators:
                if indicator in line_clean:
                    lesson_sections.append({
                        'line': i+1,
                        'week': current_week,
                        'indicator': indicator,
                        'content': line.strip()[:100]
                    })
        
        print(f"📚 Lesson structure indicators found: {len(lesson_sections)}")

        # Also count direct lesson number references
        lesson_numbers = set()
        for pattern in lesson_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if match.isdigit() and 1 <= int(match) <= 10:  # Reasonable lesson range
                    lesson_numbers.add(int(match))

        print(f"📝 Direct lesson numbers found: {sorted(lesson_numbers)}")

        # Count table rows (each row typically represents a lesson)
        table_rows = 0
        for line in lines:
            # Look for lines that seem to be table rows with multiple columns
            if '\t' in line or '|' in line:
                # Skip header rows
                if not any(header in line.lower() for header in ['wk', 'ls', 'strand', 'theme', 'sub strand']):
                    table_rows += 1

        print(f"📊 Potential table rows (lessons): {table_rows}")

        # Group by week and estimate lessons
        week_indicators = {}
        for section in lesson_sections:
            week = section['week']
            if week and week in weeks_found:
                if week not in week_indicators:
                    week_indicators[week] = []
                week_indicators[week].append(section)
        
        print("\n📋 Detailed breakdown by week:")
        total_estimated_lessons = 0
        
        for week in sorted(week_indicators.keys()):
            indicators_count = len(week_indicators[week])
            # Estimate lessons: typically 5 indicators per lesson (outcomes, questions, experiences, assessment, resources)
            estimated_lessons = max(1, indicators_count // 5)
            total_estimated_lessons += estimated_lessons
            
            print(f"   Week {week}: ~{estimated_lessons} lessons ({indicators_count} indicators)")
            
            # Show sample indicators for this week
            sample_indicators = week_indicators[week][:3]
            for indicator in sample_indicators:
                print(f"      • {indicator['indicator']}: {indicator['content'][:60]}...")
        
        print(f"\n📈 FINAL ANALYSIS:")
        print(f"   📅 Total weeks: {len(weeks_list)}")
        print(f"   📚 Estimated total lessons: {total_estimated_lessons}")
        print(f"   📊 Average lessons per week: {total_estimated_lessons / len(weeks_list) if weeks_list else 0:.1f}")
        
        # Show first few lines for context
        print(f"\n📖 Content preview (first 10 non-empty lines):")
        preview_lines = [line.strip() for line in lines if line.strip()][:10]
        for i, line in enumerate(preview_lines, 1):
            print(f"   {i:2d}: {line[:80]}{'...' if len(line) > 80 else ''}")
            
    except Exception as e:
        print(f"❌ Error analyzing PDF: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_stm_pdf()
