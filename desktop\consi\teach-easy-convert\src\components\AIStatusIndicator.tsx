"use client";

import React, { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Brain,
  CheckCircle2,
  AlertCircle,
  Cpu,
  Zap,
  Shield,
} from "lucide-react";

interface AIStatusIndicatorProps {
  className?: string;
}

export const AIStatusIndicator: React.FC<AIStatusIndicatorProps> = ({
  className = "",
}) => {
  const [aiHealth, setAIHealth] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAIHealth = async () => {
      try {
        const API_BASE_URL =
          process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";
        const response = await fetch(`${API_BASE_URL}/ai-health/`);
        if (response.ok) {
          const data = await response.json();
          setAIHealth(data);
        }
      } catch (error) {
        console.error("Failed to check AI health:", error);
        setAIHealth({ status: "unavailable" });
      } finally {
        setIsLoading(false);
      }
    };

    checkAIHealth();
  }, []);

  if (isLoading) {
    return (
      <div
        className={`flex items-center gap-2 px-4 py-2 bg-slate-100 rounded-full ${className}`}
      >
        <div className="w-2 h-2 bg-slate-400 rounded-full animate-pulse"></div>
        <span className="text-sm text-slate-600 font-medium">
          Checking AI status...
        </span>
      </div>
    );
  }

  const isHealthy = aiHealth?.status === "healthy";
  const modelsCount = aiHealth?.models_available?.length || 0;

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Main Status */}
      <div
        className={`flex items-center gap-2 px-4 py-2 rounded-full ${
          isHealthy
            ? "bg-green-100 dark:bg-green-900/20"
            : "bg-red-100 dark:bg-red-900/20"
        }`}
      >
        <div
          className={`w-2 h-2 rounded-full ${
            isHealthy ? "bg-green-500 animate-pulse" : "bg-red-500"
          }`}
        ></div>
        <span
          className={`text-sm font-medium ${
            isHealthy
              ? "text-green-700 dark:text-green-300"
              : "text-red-700 dark:text-red-300"
          }`}
        >
          {isHealthy ? (
            <>
              <Brain className="inline w-4 h-4 mr-1" />
              AI System Online - {modelsCount} Models Active
            </>
          ) : (
            <>
              <AlertCircle className="inline w-4 h-4 mr-1" />
              AI System Offline
            </>
          )}
        </span>
      </div>

      {/* Detailed Status */}
      {isHealthy && (
        <div className="flex flex-wrap gap-2 justify-center">
          <Badge
            variant="outline"
            className="bg-blue-500/20 text-blue-700 dark:text-blue-300 border-blue-500/50 text-xs"
          >
            <Cpu className="w-3 h-3 mr-1" />
            Ensemble Processing
          </Badge>
          <Badge
            variant="outline"
            className="bg-purple-500/20 text-purple-700 dark:text-purple-300 border-purple-500/50 text-xs"
          >
            <Shield className="w-3 h-3 mr-1" />
            {(aiHealth.accuracy_threshold * 100).toFixed(0)}% Accuracy
          </Badge>
          <Badge
            variant="outline"
            className="bg-orange-500/20 text-orange-700 dark:text-orange-300 border-orange-500/50 text-xs"
          >
            <Zap className="w-3 h-3 mr-1" />
            {(aiHealth.confidence_threshold * 100).toFixed(0)}% Confidence
          </Badge>
        </div>
      )}
    </div>
  );
};

export default AIStatusIndicator;
