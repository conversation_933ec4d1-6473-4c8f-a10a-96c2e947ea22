"""
Enhanced Scheme of Work Parser with robust handling for various formats
"""
import re
import fitz  # PyMuPDF
from typing import Dict, List, Tuple, Optional
import logging

class EnhancedSchemeParser:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Enhanced column patterns - more flexible matching
        self.column_patterns = {
            'week': [
                r'week\s*(\d+)', r'wk\s*(\d+)', r'w(\d+)', r'week:?\s*(\d+)',
                r'^\s*(\d+)\s*$', r'week\s+(\d+)', r'(\d+)\s*week'
            ],
            'lesson': [
                r'lesson\s*(\d+)', r'lsn\s*(\d+)', r'l(\d+)', r'lesson:?\s*(\d+)',
                r'period\s*(\d+)', r'lesson\s+(\d+)', r'(\d+)\s*lesson'
            ],
            'strand': [
                r'strand[s]?[:\-]?\s*(.+)', r'theme[s]?[:\-]?\s*(.+)', 
                r'topic[s]?[:\-]?\s*(.+)', r'main\s+topic[s]?[:\-]?\s*(.+)',
                r'subject\s+area[s]?[:\-]?\s*(.+)', r'content\s+area[s]?[:\-]?\s*(.+)'
            ],
            'sub_strand': [
                r'sub[\-\s]*strand[s]?[:\-]?\s*(.+)', r'sub[\-\s]*topic[s]?[:\-]?\s*(.+)',
                r'sub[\-\s]*theme[s]?[:\-]?\s*(.+)', r'substrand[s]?[:\-]?\s*(.+)',
                r'specific\s+topic[s]?[:\-]?\s*(.+)', r'focus\s+area[s]?[:\-]?\s*(.+)'
            ],
            'learning_outcomes': [
                r'specific\s+learning\s+outcome[s]?[:\-]?\s*(.+)',
                r'learning\s+outcome[s]?[:\-]?\s*(.+)', 
                r'objective[s]?[:\-]?\s*(.+)', r'slo[:\-]?\s*(.+)',
                r'expected\s+outcome[s]?[:\-]?\s*(.+)', r'goal[s]?[:\-]?\s*(.+)',
                r'by\s+the\s+end\s+of\s+the\s+lesson[,\s]*(.+)'
            ],
            'learning_experiences': [
                r'learning\s+experience[s]?[:\-]?\s*(.+)',
                r'learning[\s/]+teaching\s+experience[s]?[:\-]?\s*(.+)',
                r'activit(?:y|ies)[:\-]?\s*(.+)', r'procedure[s]?[:\-]?\s*(.+)',
                r'teaching\s+activit(?:y|ies)[:\-]?\s*(.+)',
                r'learning\s+activit(?:y|ies)[:\-]?\s*(.+)'
            ],
            'key_inquiry_questions': [
                r'key\s+inquiry\s+question[s]?[:\-]?\s*(.+)',
                r'inquiry\s+question[s]?[:\-]?\s*(.+)', 
                r'kiq[:\-]?\s*(.+)', r'guiding\s+question[s]?[:\-]?\s*(.+)',
                r'essential\s+question[s]?[:\-]?\s*(.+)'
            ],
            'learning_resources': [
                r'learning\s+resource[s]?[:\-]?\s*(.+)',
                r'resource[s]?[:\-]?\s*(.+)', r'material[s]?[:\-]?\s*(.+)',
                r'learning\s+material[s]?[:\-]?\s*(.+)',
                r'teaching\s+material[s]?[:\-]?\s*(.+)',
                r'teaching\s+aid[s]?[:\-]?\s*(.+)', r'reference[s]?[:\-]?\s*(.+)'
            ],
            'assessment': [
                r'assessment[:\-]?\s*(.+)', r'evaluation[:\-]?\s*(.+)',
                r'assessment\s+method[s]?[:\-]?\s*(.+)',
                r'assessment\s+technique[s]?[:\-]?\s*(.+)',
                r'evaluation\s+method[s]?[:\-]?\s*(.+)'
            ],
            'reflection': [
                r'reflection[s]?[:\-]?\s*(.+)', r'self[\-\s]*reflection[s]?[:\-]?\s*(.+)',
                r'teacher\s+reflection[s]?[:\-]?\s*(.+)', r'remark[s]?[:\-]?\s*(.+)'
            ]
        }
        
        # Enhanced CBC-specific strand patterns
        self.cbc_strand_patterns = {
            # Mathematics strands
            'numbers': r'(?:numbers?|number\s+concepts?|numeration|counting|place\s+value)',
            'geometry': r'(?:geometry|shapes?|spatial|3d|2d|geometric|space)',
            'measurement': r'(?:measurement|measuring|length|mass|time|capacity|volume)',
            'data': r'(?:data|statistics|graphs?|charts?|probability)',
            'algebra': r'(?:algebra|patterns?|equations?|expressions?)',
            'money': r'(?:money|currency|coins?|notes?|buying|selling)',
            
            # Science strands
            'living_things': r'(?:living\s+things?|life|biology|plants?|animals?|human\s+body|organisms?)',
            'non_living': r'(?:non[\-\s]*living|matter|materials?|substances?|physics)',
            'energy': r'(?:energy|force|motion|electricity|magnetism|heat|light|sound)',
            'environment': r'(?:environment|ecology|conservation|pollution|weather|climate)',
            'health': r'(?:health|hygiene|nutrition|disease|safety|first\s+aid)',
            
            # Language strands
            'listening': r'(?:listening|listening\s+skills?|comprehension)',
            'speaking': r'(?:speaking|oral|conversation|presentation)',
            'reading': r'(?:reading|literacy|comprehension|phonics)',
            'writing': r'(?:writing|composition|spelling|grammar|handwriting)',
            
            # Social Studies strands
            'history': r'(?:history|historical|past|heritage|culture)',
            'geography': r'(?:geography|maps?|location|physical\s+features?)',
            'citizenship': r'(?:citizenship|civic|government|rights|responsibilities)',
            'economics': r'(?:economics?|trade|resources?|production)',
            
            # Creative Arts strands
            'visual_arts': r'(?:visual\s+arts?|drawing|painting|crafts?|art)',
            'performing_arts': r'(?:performing\s+arts?|music|dance|drama|theatre)',
            'digital_arts': r'(?:digital\s+arts?|computer\s+arts?|multimedia)',
            
            # ICT strands
            'computing': r'(?:computing|computer|ict|technology|digital)',
            'programming': r'(?:programming|coding|algorithms?|software)',
            'internet': r'(?:internet|web|online|networking)',
            
            # Physical Education strands
            'motor_skills': r'(?:motor\s+skills?|movement|coordination|balance)',
            'games': r'(?:games?|sports?|athletics|competition)',
            'fitness': r'(?:fitness|exercise|physical\s+activity)',
            
            # Religious Education strands
            'beliefs': r'(?:beliefs?|faith|doctrine|teachings?)',
            'practices': r'(?:practices?|worship|prayer|rituals?)',
            'values': r'(?:values?|morals?|ethics?|character)',
            
            # Pre-Technical Studies strands
            'materials': r'(?:materials?|composite\s+materials?|wood|metal|plastic|ceramics?|textiles?)',
            'tools': r'(?:tools?|equipment|measuring\s+tools?|cutting\s+tools?|hand\s+tools?)',
            'processes': r'(?:processes?|manufacturing|production|assembly|joining|finishing)',
            'design': r'(?:design|designing|technical\s+drawing|drafting|sketching)',
            'safety': r'(?:safety|workshop\s+safety|health\s+and\s+safety|protective\s+equipment)',
            'technology': r'(?:technology|technological|innovation|invention)',
        }
        
        # Common separators and indicators
        self.separators = ['|', '\t', '  ', '   ', '    ']
        self.bullet_points = ['•', '○', '▪', '-', '*', '→', '◦']
        
    def clean_extracted_content(self, content: str) -> str:
        """Clean unwanted content like advertisements, phone numbers, etc."""
        if not content:
            return content
            
        # Remove phone numbers (various formats)
        content = re.sub(r'\b\d{4}\s*\d{3}\s*\d{3}\b', '', content)
        content = re.sub(r'\b07\d{8}\b', '', content)
        content = re.sub(r'\b\+254\s*\d{9}\b', '', content)
        
        # Remove advertisement text and unwanted content
        ad_patterns = [
            r'schemes\s+of\s+work\s*,?\s*notes\s*,?\s*exams\s+and\s+lessons?\s+plans?\s+call\s+sir\s+abraham[^.]*',
            r'search\s+the\s+internet\s+for\s+more\s+information[^.]*',
            r'call\s+sir\s+\w+[^.]*',
            r'for\s+more\s+notes\s+call[^.]*',
            r'visit\s+our\s+website[^.]*',
            r'download\s+more\s+resources[^.]*',
            r'sir\s+abraham\s+\d+[^.]*',
            r'0729\s*125\s*181[^.]*',
            r'by\s+the\s+end\s+of\s+the\s+lesson,?\s*learners\s+will\s+be\s+able\s+to\s+understand\s+the\s+topic\.?',
            r'by\s+the\s+end\s+of\s+the\s+lesson,?\s*the\s+learner\s+should\s+be\s+able\s+to\s+by\s+the\s+end',
        ]
        
        for pattern in ad_patterns:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
        
        # Remove redundant "by the end" repetitions
        content = re.sub(r'by\s+the\s+end\s+of\s+the\s+lesson,?\s*by\s+the\s+end\s+of\s+the\s+lesson,?', 'by the end of the lesson,', content, flags=re.IGNORECASE)
        
        # Clean multiple spaces and normalize
        content = re.sub(r'\s+', ' ', content).strip()
        
        # Remove leading/trailing punctuation except periods and question marks
        content = re.sub(r'^[,\-\s]+|[,\-\s]+$', '', content)
        
        return content

    def extract_text_from_pdf(self, file_content: bytes) -> str:
        """Enhanced PDF text extraction with layout preservation"""
        try:
            doc = fitz.open(stream=file_content, filetype="pdf")
            full_text = ""
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Try to preserve table structure
                text_dict = page.get_text("dict")
                blocks = text_dict.get("blocks", [])
                
                page_text = ""
                for block in blocks:
                    if "lines" in block:
                        for line in block["lines"]:
                            line_text = ""
                            for span in line["spans"]:
                                line_text += span.get("text", "")
                            if line_text.strip():
                                page_text += line_text + "\n"
                
                # Fallback to simple text extraction if dict method fails
                if not page_text.strip():
                    page_text = page.get_text("text")
                
                full_text += f"--- PAGE {page_num + 1} ---\n" + page_text + "\n"
                
            doc.close()
            return full_text
            
        except Exception as e:
            self.logger.error(f"PDF extraction error: {e}")
            raise Exception(f"Failed to extract text from PDF: {str(e)}")
    
    def detect_table_structure(self, text: str) -> Dict[str, List[str]]:
        """Detect if content is in table format and extract column headers"""
        lines = text.split('\n')
        
        # Look for header patterns - more flexible approach
        potential_headers = []
        
        # Look for lines that contain multiple key column indicators
        header_indicators = [
            'week', 'lesson', 'strand', 'sub-strand', 'learning', 'outcome', 
            'experience', 'inquiry', 'question', 'resource', 'assessment', 'method'
        ]
        
        for i, line in enumerate(lines[:30]):  # Check first 30 lines for headers
            line = line.strip().lower()
            if not line:
                continue
            
            # Count how many header indicators are in this line
            indicator_count = sum(1 for indicator in header_indicators if indicator in line)
            
            if indicator_count >= 4:  # If line contains multiple indicators, likely a header
                # Try different splitting strategies
                original_line = lines[i].strip()
                
                # Strategy 1: Split by multiple spaces
                if '  ' in original_line:
                    columns = [col.strip() for col in original_line.split('  ') if col.strip()]
                    if len(columns) >= 4:
                        potential_headers.append((i, columns, '  '))
                
                # Strategy 2: Look for tab characters
                if '\t' in original_line:
                    columns = [col.strip() for col in original_line.split('\t') if col.strip()]
                    if len(columns) >= 4:
                        potential_headers.append((i, columns, '\t'))
                
                # Strategy 3: Split by single spaces if we have many words
                words = original_line.split()
                if len(words) >= 6:  # Many words might indicate column headers
                    potential_headers.append((i, words, ' '))
        
        return potential_headers
    
    def parse_table_format(self, text: str) -> List[Dict]:
        """Parse table-formatted scheme of work - enhanced for Pre-Technical format"""
        lessons = []
        
        # Look for the specific Pre-Technical table format
        # The table has headers: WK LSN STRAND SUB-STRAND LESSON LEARNING OUTCOME etc.
        
        # Find the table start by looking for the header row
        lines = text.split('\n')
        table_start_idx = -1
        
        for i, line in enumerate(lines):
            line_upper = line.upper().strip()
            # Look for the header row with multiple column indicators
            if ('WK' in line_upper and 'LSN' in line_upper and 'STRAND' in line_upper and 
                'SUB-STRAND' in line_upper and 'OUTCOME' in line_upper):
                table_start_idx = i
                break
            # Alternative header patterns
            elif ('WEEK' in line_upper and 'LESSON' in line_upper and 'STRAND' in line_upper):
                table_start_idx = i
                break
        
        if table_start_idx == -1:
            # Fall back to free format parsing
            return self.parse_free_format(text)
        
        # Parse table rows after header
        current_lesson = None
        collecting_content = False
        content_buffer = []
        
        for i in range(table_start_idx + 1, len(lines)):
            line = lines[i].strip()
            
            # Skip empty lines and page headers
            if not line or 'SCHEMES OF WORK' in line.upper() or 'CALL SIR' in line.upper():
                continue
            
            # Check if this line starts a new lesson (has week/lesson numbers)
            week_lesson_match = re.match(r'^(\d+)\s+(\d+)\s+(.+)', line)
            if week_lesson_match:
                # Save previous lesson if exists
                if current_lesson:
                    self._finalize_lesson_content(current_lesson, content_buffer)
                    lessons.append(current_lesson)
                
                # Start new lesson
                week_num = int(week_lesson_match.group(1))
                lesson_num = int(week_lesson_match.group(2))
                rest_of_line = week_lesson_match.group(3).strip()
                
                current_lesson = {
                    'week': week_num,
                    'lessonNumber': lesson_num,
                    'raw_content': [rest_of_line]
                }
                content_buffer = [rest_of_line]
                collecting_content = True
                
            elif collecting_content and current_lesson:
                # Continue collecting content for current lesson
                content_buffer.append(line)
                current_lesson['raw_content'].append(line)
        
        # Save the last lesson
        if current_lesson:
            self._finalize_lesson_content(current_lesson, content_buffer)
            lessons.append(current_lesson)
        
        return lessons
    
    def _finalize_lesson_content(self, lesson: Dict, content_buffer: List[str]):
        """Extract lesson components from collected content"""
        full_content = ' '.join(content_buffer)
        
        # Extract different components using patterns
        self._extract_strand_from_content(lesson, full_content)
        self._extract_sub_strand_from_content(lesson, full_content) 
        self._extract_learning_outcomes_from_content(lesson, full_content)
        self._extract_learning_experiences_from_content(lesson, full_content)
        self._extract_inquiry_question_from_content(lesson, full_content)
        self._extract_resources_from_content(lesson, full_content)
        self._extract_assessment_from_content(lesson, full_content)
    
    def _extract_strand_from_content(self, lesson: Dict, content: str):
        """Extract strand from the content"""
        # The strand usually appears early in the content
        words = content.split()
        
        # Look for Pre-Technical specific strands
        if any(word.lower() in ['tools', 'holding', 'cutting', 'measuring'] for word in words[:10]):
            lesson['strand'] = 'Pre-Technical Studies'
        elif any(word.lower() in ['materials', 'composite', 'wood', 'metal'] for word in words[:10]):
            lesson['strand'] = 'Pre-Technical Studies'
        elif any(word.lower() in ['design', 'drawing', 'sketch'] for word in words[:10]):
            lesson['strand'] = 'Pre-Technical Studies'
        else:
            lesson['strand'] = 'Pre-Technical Studies'  # Default for this scheme
    
    def _extract_sub_strand_from_content(self, lesson: Dict, content: str):
        """Extract sub-strand from content"""
        content_lower = content.lower()
        
        # Pre-Technical specific sub-strand patterns
        if any(word in content_lower for word in ['holding', 'cutting', 'measuring', 'hand tools']):
            lesson['sub_strand'] = 'Tools and Equipment'
        elif any(word in content_lower for word in ['materials', 'composite', 'wood', 'metal', 'plastic']):
            lesson['sub_strand'] = 'Materials and Their Properties'
        elif any(word in content_lower for word in ['design', 'drawing', 'sketch', 'technical drawing']):
            lesson['sub_strand'] = 'Design and Drawing'
        elif any(word in content_lower for word in ['safety', 'protective', 'hazard', 'workshop safety']):
            lesson['sub_strand'] = 'Safety and Health'
        elif any(word in content_lower for word in ['process', 'manufacturing', 'production', 'assembly']):
            lesson['sub_strand'] = 'Processes and Techniques'
        else:
            # Extract from the first few words that might be the sub-strand
            words = content.split()
            if len(words) >= 2:
                potential_substrand = ' '.join(words[:3]).title()
                lesson['sub_strand'] = potential_substrand
            else:
                lesson['sub_strand'] = 'General Topic'
    
    def _extract_learning_outcomes_from_content(self, lesson: Dict, content: str):
        """Extract learning outcomes from content"""
        # Look for "By the end of the lesson" pattern
        outcome_pattern = r'by\s+the\s+end.*?learner.*?able\s+to[:\s]*([^?]+?)(?:in\s+groups|which|$)'
        match = re.search(outcome_pattern, content, re.IGNORECASE | re.DOTALL)
        
        if match:
            outcomes_text = self.clean_extracted_content(match.group(1))
            
            # Split by a), b), c) pattern
            outcomes = []
            parts = re.split(r'[a-e]\)\s*', outcomes_text)
            for part in parts[1:]:  # Skip first empty part
                clean_outcome = self.clean_extracted_content(part.strip())
                if clean_outcome and len(clean_outcome) > 5:
                    outcomes.append(clean_outcome)
            
            if outcomes:
                lesson['specific_learning_outcomes'] = outcomes
            else:
                lesson['specific_learning_outcomes'] = [outcomes_text] if outcomes_text else []
        else:
            lesson['specific_learning_outcomes'] = []
    
    def _extract_learning_experiences_from_content(self, lesson: Dict, content: str):
        """Extract learning experiences from content"""
        # Look for "In groups" or similar patterns that indicate learning experiences
        experience_patterns = [
            r'in\s+groups.*?guided\s+to[:\s]*([^?]+?)(?:which|$)',
            r'learners?\s+are\s+guided\s+to[:\s]*([^?]+?)(?:which|$)',
            r'pupils?\s+will[:\s]*([^?]+?)(?:which|$)'
        ]
        
        experiences = []
        for pattern in experience_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                exp_text = self.clean_extracted_content(match.group(1))
                if exp_text:
                    # Split by periods or line breaks to get individual experiences
                    exp_parts = re.split(r'[.]\s*', exp_text)
                    for part in exp_parts:
                        clean_part = part.strip()
                        if clean_part and len(clean_part) > 10:
                            experiences.append(clean_part)
                break
        
        lesson['learning_experiences'] = experiences
    
    def _extract_inquiry_question_from_content(self, lesson: Dict, content: str):
        """Extract key inquiry question from content"""
        # Look for question patterns
        question_patterns = [
            r'which\s+[^?]*?\?',
            r'what\s+[^?]*?\?', 
            r'how\s+[^?]*?\?',
            r'why\s+[^?]*?\?'
        ]
        
        for pattern in question_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                question = self.clean_extracted_content(match.group(0))
                lesson['key_inquiry_question'] = question.capitalize()
                return
        
        lesson['key_inquiry_question'] = ''
    
    def _extract_resources_from_content(self, lesson: Dict, content: str):
        """Extract learning resources from content"""
        # Look for resource indicators
        resource_indicators = ['oxford', 'textbook', 'pictures', 'digital devices', 'charts', 'notes']
        resources = []
        
        for indicator in resource_indicators:
            if indicator.lower() in content.lower():
                # Try to extract the full resource name
                pattern = r'([^.]*?' + re.escape(indicator) + r'[^.]*?)(?:\.|$)'
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    resource = self.clean_extracted_content(match.group(1).strip())
                    if resource and len(resource) < 100:
                        resources.append(resource.title())
        
        lesson['learning_resources'] = list(set(resources)) if resources else []
    
    def _extract_assessment_from_content(self, lesson: Dict, content: str):
        """Extract assessment information from content"""
        # Look for assessment-related keywords
        assessment_keywords = ['observation', 'oral', 'written', 'practical', 'demonstration']
        
        for keyword in assessment_keywords:
            if keyword in content.lower():
                lesson['assessment'] = f"Assessment through {keyword} and other appropriate methods"
                return
        
        lesson['assessment'] = ''
    
    def map_headers_to_fields(self, headers: List[str]) -> Dict[int, str]:
        """Map table headers to our standard field names"""
        mapping = {}
        
        for idx, header in enumerate(headers):
            header_lower = header.lower().strip()
            
            # Map each header to closest field
            for field, patterns in self.column_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, header_lower):
                        mapping[idx] = field
                        break
                if idx in mapping:
                    break
        
        return mapping
    
    def extract_lesson_from_row(self, columns: List[str], header_mapping: Dict[int, str]) -> Optional[Dict]:
        """Extract lesson data from a table row"""
        lesson = {}
        
        # Initialize with defaults
        default_lesson = {
            'week': None, 'lessonNumber': 1, 'title': '', 'strand': '', 'sub_strand': '',
            'specific_learning_outcomes': [], 'core_competencies': [],
            'key_inquiry_question': '', 'learning_resources': [],
            'activities': [], 'assessment': '', 'reflection': ''
        }
        
        lesson.update(default_lesson)
        
        # Extract data from mapped columns
        for col_idx, field_name in header_mapping.items():
            if col_idx < len(columns):
                content = columns[col_idx].strip()
                if content:
                    if field_name in ['week', 'lessonNumber']:
                        # Extract numbers
                        match = re.search(r'(\d+)', content)
                        if match:
                            lesson[field_name] = int(match.group(1))
                    elif field_name in ['specific_learning_outcomes', 'learning_resources', 'activities', 'core_competencies']:
                        # List fields
                        lesson[field_name] = self.split_list_content(content)
                    else:
                        # String fields
                        lesson[field_name] = content
        
        # Set title if not present
        if not lesson['title'] and lesson['strand']:
            lesson['title'] = f"{lesson['strand']}: {lesson['sub_strand']}"
        
        # Only return if we have minimum viable data
        if lesson['week'] and (lesson['strand'] or lesson['title'] or lesson['specific_learning_outcomes']):
            return lesson
        
        return None
    
    def split_list_content(self, content: str) -> List[str]:
        """Split content into list items"""
        if not content:
            return []
        
        # Try different splitting strategies
        items = []
        
        # Split by bullet points
        for bullet in self.bullet_points:
            if bullet in content:
                items = [item.strip() for item in content.split(bullet) if item.strip()]
                break
        
        # Split by common separators if no bullets found
        if not items:
            for sep in [';', '\n', '|']:
                if sep in content:
                    items = [item.strip() for item in content.split(sep) if item.strip()]
                    break
        
        # If still no items, return as single item
        if not items:
            items = [content]
        
        return items
    
    def parse_free_format(self, text: str) -> List[Dict]:
        """Parse free-format text when table structure is not clear"""
        lessons = []
        lines = text.split('\n')
        
        # Look for week/lesson patterns in the text
        current_lesson = None
        current_field = None
        
        # Enhanced week detection patterns
        week_patterns = [
            r'(?:week|wk|w)\s*[:\-]?\s*(\d+)',
            r'^(\d+)\s*$',  # Just a number on its own line
            r'(\d+)\s+(?:week|wk)',
            r'^(\d+)\s+\d+',  # Pattern like "1 1" (week lesson)
        ]
        
        # Track content blocks for each lesson
        lesson_blocks = []
        current_block = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Check if this line indicates a new week/lesson
            found_week = False
            for pattern in week_patterns:
                match = re.search(pattern, line.lower())
                if match:
                    # Save previous block if exists
                    if current_block:
                        lesson_blocks.append(current_block.copy())
                        current_block = []
                    
                    week_num = int(match.group(1))
                    current_block = [('week', week_num), ('raw_content', [line])]
                    found_week = True
                    break
            
            if not found_week and current_block:
                # Add to current block
                current_block.append(('content', line))
        
        # Save the last block
        if current_block:
            lesson_blocks.append(current_block)
        
        # Process each lesson block
        for block in lesson_blocks:
            lesson = self.extract_lesson_from_block(block)
            if lesson:
                lessons.append(lesson)
        
        return lessons
    
    def extract_lesson_from_block(self, block: List) -> Optional[Dict]:
        """Extract lesson data from a content block"""
        lesson = {
            'week': None, 'lessonNumber': 1, 'title': '', 'strand': '', 'sub_strand': '',
            'specific_learning_outcomes': [], 'core_competencies': [],
            'key_inquiry_question': '', 'learning_resources': [],
            'activities': [], 'assessment': '', 'reflection': ''
        }
        
        # Extract week number
        for item_type, content in block:
            if item_type == 'week':
                lesson['week'] = content
                break
        
        # Process content lines
        content_lines = []
        for item_type, content in block:
            if item_type == 'content':
                content_lines.append(content)
        
        # Join all content and try to extract information
        full_content = ' '.join(content_lines)
        
        # Use patterns to extract different sections
        self.extract_lesson_components(full_content, lesson)
        
        # Only return if we have minimum viable data
        if lesson['week'] and (lesson['strand'] or lesson['title'] or lesson['specific_learning_outcomes']):
            return lesson
        
        return None
    
    def identify_strand_from_content(self, content: str) -> str:
        """Enhanced strand identification using improved CBC-specific logic"""
        # Import here to avoid circular imports
        try:
            from improved_strand_identifier import ImprovedStrandIdentifier
            identifier = ImprovedStrandIdentifier()
            return identifier.identify_strand(content)
        except ImportError:
            # Fallback to original logic if import fails
            return self._fallback_strand_identification(content)
    
    def _fallback_strand_identification(self, content: str) -> str:
        """Fallback strand identification method"""
        content_lower = content.lower()
        
        # First, try to find explicit strand mentions
        explicit_patterns = [
            r'strand[s]?[:\-]?\s*([A-Za-z\s]+?)(?:\s+sub|$|\.|,|;)',
            r'theme[s]?[:\-]?\s*([A-Za-z\s]+?)(?:\s+sub|$|\.|,|;)',
            r'topic[s]?[:\-]?\s*([A-Za-z\s]+?)(?:\s+sub|$|\.|,|;)',
        ]
        
        for pattern in explicit_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                strand = match.group(1).strip()
                if len(strand) > 2 and len(strand) < 50:  # Reasonable length
                    return self.normalize_strand_name(strand)
        
        # If no explicit strand found, use CBC pattern matching
        best_match = None
        best_score = 0
        
        for strand_name, pattern in self.cbc_strand_patterns.items():
            matches = re.findall(pattern, content_lower, re.IGNORECASE)
            if matches:
                # Score based on number of matches and position
                score = len(matches)
                # Boost score if found early in content (likely more important)
                early_match = re.search(pattern, content_lower[:200], re.IGNORECASE)
                if early_match:
                    score += 2
                
                if score > best_score:
                    best_score = score
                    best_match = strand_name
        
        if best_match:
            return self.normalize_strand_name(best_match.replace('_', ' ').title())
        
        # Fallback: try to extract capitalized words that might be strands
        capitalized_words = re.findall(r'\b[A-Z][A-Z\s]+\b', content)
        for word in capitalized_words:
            word = word.strip()
            if 3 <= len(word) <= 30 and word not in ['THE', 'AND', 'FOR', 'WITH']:
                return self.normalize_strand_name(word.title())
        
        return "General"
    
    def identify_substrand_from_content(self, content: str, strand: str) -> str:
        """Enhanced sub-strand identification using improved logic"""
        # Import here to avoid circular imports
        try:
            from improved_strand_identifier import ImprovedStrandIdentifier
            identifier = ImprovedStrandIdentifier()
            return identifier.identify_substrand(content, strand)
        except ImportError:
            # Fallback to original logic if import fails
            return self._fallback_substrand_identification(content, strand)
    
    def _fallback_substrand_identification(self, content: str, strand: str) -> str:
        """Fallback sub-strand identification method"""
        content_lower = content.lower()
        
        # Look for explicit sub-strand patterns
        substrand_patterns = [
            r'sub[\-\s]*strand[s]?[:\-]?\s*([A-Za-z\s]+?)(?:\s+by\s+the\s+end|$|\.|,|;)',
            r'sub[\-\s]*topic[s]?[:\-]?\s*([A-Za-z\s]+?)(?:\s+by\s+the\s+end|$|\.|,|;)',
            r'specific\s+topic[s]?[:\-]?\s*([A-Za-z\s]+?)(?:\s+by\s+the\s+end|$|\.|,|;)',
        ]
        
        for pattern in substrand_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                substrand = match.group(1).strip()
                if len(substrand) > 2 and len(substrand) < 100:
                    return self.normalize_strand_name(substrand)
        
        # If strand is identified, look for related specific topics
        if strand != "General":
            # Extract content that comes after the strand mention
            strand_pattern = re.escape(strand.lower())
            match = re.search(f'{strand_pattern}[:\-]?\s*([A-Za-z\s,]+?)(?:\s+by\s+the\s+end|$)', content_lower)
            if match:
                potential_substrand = match.group(1).strip()
                # Clean up common prefixes/suffixes
                potential_substrand = re.sub(r'^(and|or|the|a|an)\s+', '', potential_substrand)
                potential_substrand = re.sub(r'\s+(and|or|the|a|an)$', '', potential_substrand)
                if len(potential_substrand) > 2:
                    return self.normalize_strand_name(potential_substrand.title())
        
        # Look for descriptive phrases that might be sub-strands
        descriptive_patterns = [
            r'([A-Za-z\s]+?)\s+concepts?',
            r'([A-Za-z\s]+?)\s+skills?',
            r'([A-Za-z\s]+?)\s+activities?',
            r'([A-Za-z\s]+?)\s+methods?',
            r'([A-Za-z\s]+?)\s+techniques?',
        ]
        
        for pattern in descriptive_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                substrand = match.group(1).strip()
                if 3 <= len(substrand) <= 50:
                    return self.normalize_strand_name(substrand.title())
        
        return "General Topic"
    
    def normalize_strand_name(self, strand: str) -> str:
        """Normalize strand names to standard CBC format"""
        strand = strand.strip()
        
        # Common normalizations
        normalizations = {
            'maths': 'Mathematics',
            'math': 'Mathematics',
            'numbers': 'Numbers',
            'geometry': 'Geometry',
            'measurement': 'Measurement',
            'data': 'Data Handling',
            'science': 'Science',
            'living things': 'Living Things',
            'non living': 'Non-Living Things',
            'energy': 'Energy',
            'environment': 'Environment',
            'health': 'Health Education',
            'english': 'English',
            'kiswahili': 'Kiswahili',
            'listening': 'Listening and Speaking',
            'speaking': 'Listening and Speaking',
            'reading': 'Reading',
            'writing': 'Writing',
            'social studies': 'Social Studies',
            'history': 'History',
            'geography': 'Geography',
            'citizenship': 'Citizenship',
            'creative arts': 'Creative Arts',
            'visual arts': 'Visual Arts',
            'performing arts': 'Performing Arts',
            'pe': 'Physical Education',
            'physical education': 'Physical Education',
            'ict': 'ICT',
            'computing': 'ICT',
            'computer': 'ICT',
            're': 'Religious Education',
            'religious education': 'Religious Education',
            'pre-technical': 'Pre-Technical Studies',
            'pre technical': 'Pre-Technical Studies',
            'pretechnical': 'Pre-Technical Studies',
            'technical studies': 'Pre-Technical Studies',
            'materials': 'Materials and Technology',
            'tools': 'Tools and Equipment',
            'processes': 'Manufacturing Processes',
            'design': 'Design and Technology',
            'workshop': 'Workshop Safety',
        }
        
        strand_lower = strand.lower()
        if strand_lower in normalizations:
            return normalizations[strand_lower]
        
        # Capitalize properly
        return ' '.join(word.capitalize() for word in strand.split())
    
    def extract_lesson_components(self, content: str, lesson: Dict):
        """Enhanced lesson component extraction with better strand identification"""
        content_lower = content.lower()
        
        # Initialize lesson with default values
        if 'strand' not in lesson:
            lesson['strand'] = ''
        if 'sub_strand' not in lesson:
            lesson['sub_strand'] = ''
        if 'specific_learning_outcomes' not in lesson:
            lesson['specific_learning_outcomes'] = []
        if 'key_inquiry_question' not in lesson:
            lesson['key_inquiry_question'] = ''
        if 'title' not in lesson:
            lesson['title'] = ''
        
        # Use enhanced strand identification
        lesson['strand'] = self.identify_strand_from_content(content)
        lesson['sub_strand'] = self.identify_substrand_from_content(content, lesson['strand'])
        
        # Set title if not present
        if not lesson['title']:
            if lesson['strand'] and lesson['sub_strand']:
                lesson['title'] = f"{lesson['strand']}: {lesson['sub_strand']}"
            elif lesson['strand']:
                lesson['title'] = lesson['strand']
        
        # Extract learning outcomes (look for "by the end" pattern)
        outcome_patterns = [
            r'by\s+the\s+end\s+of\s+the\s+lesson,?\s*(?:the\s+)?learners?\s+(?:will\s+be\s+able\s+to|should\s+be\s+able\s+to)[:\s]*([^?]+?)(?:\s+how\s+|$|\n|\.)',
            r'specific\s+learning\s+outcomes?[:\s]*([^?]+?)(?:\s+how\s+|$|\n)',
            r'objectives?[:\s]*([^?]+?)(?:\s+how\s+|$|\n)',
            r'learning\s+outcomes?[:\s]*([^?]+?)(?:\s+how\s+|$|\n)',
        ]
        
        for pattern in outcome_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                outcomes_text = self.clean_extracted_content(match.group(1))
                if not outcomes_text or len(outcomes_text) < 10:
                    continue
                    
                # Split by common separators
                outcomes = []
                
                # First try lettered lists (a), b), c), etc.)
                letter_pattern = r'[a-e]\)\s*([^a-e)]+?)(?=[a-e]\)|$)'
                letter_matches = re.findall(letter_pattern, outcomes_text, re.IGNORECASE)
                if letter_matches:
                    outcomes = [self.clean_extracted_content(match.strip()) for match in letter_matches]
                
                # Try bullet points
                if not outcomes:
                    for bullet in ['•', '-', '*', '○']:
                        if bullet in outcomes_text:
                            parts = outcomes_text.split(bullet)
                            outcomes = [self.clean_extracted_content(part.strip()) for part in parts[1:] if part.strip()]
                            break
                
                # Try line breaks
                if not outcomes and '\n' in outcomes_text:
                    parts = outcomes_text.split('\n')
                    outcomes = [self.clean_extracted_content(part.strip()) for part in parts if part.strip()]
                
                # If no splitting worked, use the whole text as one outcome
                if not outcomes:
                    cleaned = self.clean_extracted_content(outcomes_text.strip())
                    if cleaned and len(cleaned) > 10:
                        outcomes = [cleaned]
                
                # Filter out poor quality outcomes
                valid_outcomes = []
                for outcome in outcomes:
                    if (len(outcome) > 10 and 
                        not outcome.lower().startswith('by the end') and
                        not 'understand the topic' in outcome.lower() and
                        not 'call sir' in outcome.lower() and
                        not re.search(r'\d{10}', outcome)):  # No phone numbers
                        valid_outcomes.append(outcome)
                
                if valid_outcomes:
                    lesson['specific_learning_outcomes'] = valid_outcomes
                    break
        
        # Extract key inquiry question
        inquiry_patterns = [
            r'how\s+can\s+[^?]*?\?',
            r'what\s+[^?]*?\?',
            r'why\s+[^?]*?\?',
            r'when\s+[^?]*?\?',
            r'where\s+[^?]*?\?',
            r'inquiry\s+question[s]?[:\s]*([^.]+[?.])',
        ]
        
        for pattern in inquiry_patterns:
            match = re.search(pattern, content_lower)
            if match:
                if pattern.endswith('[?.]'):
                    lesson['key_inquiry_question'] = self.clean_extracted_content(match.group(1).strip().capitalize())
                else:
                    lesson['key_inquiry_question'] = self.clean_extracted_content(match.group(0).strip().capitalize())
                break
        
        # Extract resources with better pattern matching
        resource_indicators = [
            'textbook', 'chart', 'cards', 'materials', 'flashcards', 'marbles', 'stones', 
            'pictures', 'models', 'specimens', 'calculator', 'ruler', 'compass', 'protractor',
            'computer', 'internet', 'video', 'audio', 'map', 'globe', 'microscope'
        ]
        
        found_resources = []
        for indicator in resource_indicators:
            if indicator in content_lower:
                # Try to extract the phrase containing the indicator
                pattern = r'([^.]*?' + re.escape(indicator) + r'[^.]*?)(?:\s|$|\.)'
                match = re.search(pattern, content_lower)
                if match:
                    resource_phrase = match.group(1).strip()
                    if len(resource_phrase) < 100:  # Reasonable length
                        found_resources.append(resource_phrase.title())
        
        if found_resources:
            lesson['learning_resources'] = list(set(found_resources))  # Remove duplicates
        
        # Enhanced assessment detection
        assessment_indicators = [
            'observation', 'written', 'oral', 'questions', 'exercise', 'test', 'quiz',
            'presentation', 'project', 'assignment', 'homework', 'practical', 'demonstration'
        ]
        
        found_assessments = []
        for indicator in assessment_indicators:
            if indicator in content_lower:
                found_assessments.append(indicator.title())
        
        if found_assessments:
            lesson['assessment'] = ', '.join(set(found_assessments))
        
        # Create title if not present
        if not lesson['title'] or lesson['title'] == f"Week {lesson['week']} Lesson":
            if lesson['strand'] and lesson['sub_strand']:
                lesson['title'] = f"{lesson['strand']}: {lesson['sub_strand']}"
            elif lesson['strand']:
                lesson['title'] = lesson['strand']
    
    def enhance_lesson_data(self, lesson: Dict) -> Dict:
        """Add default values and enhancements to lesson data"""
        
        # Ensure all required fields exist
        required_fields = {
            'week': 1, 'lessonNumber': 1, 'title': '', 'strand': '', 'sub_strand': '',
            'specific_learning_outcomes': [], 'core_competencies': [],
            'key_inquiry_question': '', 'learning_resources': [],
            'activities': [], 'assessment': '', 'reflection': ''
        }
        
        for field, default_value in required_fields.items():
            if field not in lesson or not lesson[field]:
                lesson[field] = default_value
        
        # Set strand to Pre-Technical Studies if empty and we're processing Pre-Technical content
        if not lesson['strand'] or lesson['strand'] == 'General':
            lesson['strand'] = 'Pre-Technical Studies'
            
        # Set better sub-strand default
        if not lesson['sub_strand'] or lesson['sub_strand'] == 'General Topic':
            lesson['sub_strand'] = 'Materials and Their Properties'
        
        # Set better title
        if not lesson['title']:
            lesson['title'] = f"{lesson['strand']}: {lesson['sub_strand']}"
        
        # Provide better default learning outcomes for Pre-Technical Studies
        if not lesson['specific_learning_outcomes'] or (
            len(lesson['specific_learning_outcomes']) == 1 and 
            'understand the topic' in lesson['specific_learning_outcomes'][0].lower()
        ):
            if 'materials' in lesson['sub_strand'].lower():
                lesson['specific_learning_outcomes'] = [
                    "Identify different types of composite materials in their locality",
                    "Describe the composition and properties of composite materials",
                    "Explain the uses of composite materials in daily life"
                ]
            else:
                lesson['specific_learning_outcomes'] = [
                    f"Understand key concepts in {lesson['sub_strand'].lower()}",
                    f"Apply knowledge of {lesson['sub_strand'].lower()} in practical situations",
                    f"Demonstrate skills related to {lesson['sub_strand'].lower()}"
                ]
        
        # Set default competencies for Pre-Technical Studies
        if not lesson['core_competencies']:
            lesson['core_competencies'] = [
                'Critical thinking and problem solving',
                'Creativity and imagination',
                'Communication and collaboration'
            ]
        
        # Set default inquiry question
        if not lesson['key_inquiry_question']:
            lesson['key_inquiry_question'] = f"How can we apply {lesson['sub_strand'].lower()} in our daily lives?"
        
        # Set default resources
        if not lesson['learning_resources']:
            lesson['learning_resources'] = [
                'Textbooks and reference materials',
                'Real-life examples and specimens',
                'Charts and visual aids'
            ]
        
        # Set default activities
        if not lesson['activities']:
            lesson['activities'] = [
                'Introduction and brainstorming',
                'Guided exploration and investigation',
                'Group discussion and sharing',
                'Summary and reflection'
            ]
        
        # Set default assessment
        if not lesson['assessment']:
            lesson['assessment'] = 'Observation, oral questions, and practical demonstration'
            
        # Set default reflection
        if not lesson['reflection']:
            lesson['reflection'] = 'Were the learning outcomes achieved? What can be improved in future lessons?'
        
        return lesson
    
    def parse_scheme_of_work(self, file_content: bytes, filename: str = "scheme.pdf") -> Dict:
        """Alias for parse_scheme method for compatibility"""
        return self.parse_scheme(file_content, filename)
    
    def deduplicate_lessons(self, lessons: List[Dict]) -> List[Dict]:
        """Remove duplicate lessons based on week, lesson number, and content similarity"""
        if not lessons:
            return lessons
        
        unique_lessons = []
        seen_combinations = set()
        
        for lesson in lessons:
            week = lesson.get('week', 1)
            lesson_num = lesson.get('lessonNumber', 1)
            strand = lesson.get('strand', '')
            sub_strand = lesson.get('sub_strand', '')
            
            # Create a unique key for this lesson
            lesson_key = f"{week}_{lesson_num}_{strand}_{sub_strand}"
            
            # Check if we've seen this combination before
            if lesson_key not in seen_combinations:
                seen_combinations.add(lesson_key)
                unique_lessons.append(lesson)
            else:
                # If duplicate, merge with existing lesson (take the one with better content)
                existing_idx = None
                for i, existing in enumerate(unique_lessons):
                    if (existing.get('week') == week and 
                        existing.get('lessonNumber') == lesson_num and
                        existing.get('strand') == strand):
                        existing_idx = i
                        break
                
                if existing_idx is not None:
                    existing = unique_lessons[existing_idx]
                    # Keep the lesson with better learning outcomes
                    if (len(lesson.get('specific_learning_outcomes', [])) > 
                        len(existing.get('specific_learning_outcomes', []))):
                        unique_lessons[existing_idx] = lesson
        
        return unique_lessons

    def parse_scheme(self, file_content: bytes, filename: str) -> Dict:
        """Main parsing method"""
        try:
            # Extract text
            if filename.lower().endswith('.pdf'):
                text = self.extract_text_from_pdf(file_content)
            else:
                # Handle other formats (implementation needed)
                text = file_content.decode('utf-8', errors='ignore')
            
            # Parse lessons from scheme of work
            scheme_lessons = self.parse_table_format(text)
            
            # Remove duplicates
            scheme_lessons = self.deduplicate_lessons(scheme_lessons)
            
            # Import CBC lesson plan generator
            from cbc_lesson_plan_generator import CBCLessonPlanGenerator
            generator = CBCLessonPlanGenerator()
            
            # Generate proper CBC lesson plans
            lesson_plans = []
            config = {
                'school': 'CBC School',
                'level': 'Grade 8', 
                'learning_area': 'Pre-Technical Studies',
                'start_date': '2025-01-06',
                'time': '40 minutes',
                'roll': '30 students'
            }
            
            for scheme_data in scheme_lessons:
                # Enhance scheme data first
                enhanced_scheme = self.enhance_lesson_data(scheme_data)
                # Generate proper CBC lesson plan
                lesson_plan = generator.generate_lesson_plan_from_scheme_row(enhanced_scheme, config)
                lesson_plans.append(lesson_plan)
            
            # Sort by week and lesson number
            lesson_plans.sort(key=lambda x: (x.get('week', 0), x.get('lessonNumber', 0)))
            
            return {
                'success': True,
                'message': f'Successfully generated {len(lesson_plans)} CBC lesson plans from scheme of work',
                'lesson_plans': lesson_plans,
                'weeks_found': list(set(lesson.get('week', 1) for lesson in lesson_plans))
            }
            
        except Exception as e:
            self.logger.error(f"Parsing error: {e}")
            return {
                'success': False,
                'message': f'Error parsing scheme: {str(e)}',
                'lesson_plans': [],
                'weeks_found': []
            }
