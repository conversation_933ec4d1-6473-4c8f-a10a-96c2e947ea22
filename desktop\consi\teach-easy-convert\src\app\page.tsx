"use client";

import React, { useState, useEffect } from "react";
import FileUpload from "@/components/FileUploadWorking";
import {
  LessonPlanConfig,
  LessonPlanConfiguration,
} from "@/components/LessonPlanConfig";
import { LessonPlanEditor } from "@/components/LessonPlanEditor";
import { SampleData } from "@/components/SampleData";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  BookOpen,
  Zap,
  Users,
  CheckCircle,
  Moon,
  Sun,
  GraduationCap,
  TrendingUp,
  Award,
  Sparkles,
} from "lucide-react";
import { useTheme } from "next-themes";
import { LessonPlan, getCoreCompetencies } from "@/types/LessonPlan";
import { ParsedSchemeData, ParsingResult } from "@/utils/schemeParser";
import { toast } from "@/components/ui/use-toast";
import AIMetricsDashboard from "@/components/AIMetricsDashboard";

const Index = () => {
  const [uploadedContent, setUploadedContent] = useState<string>("");
  const [parsedData, setParsedData] = useState<any>(null);
  const [lessonPlans, setLessonPlans] = useState<LessonPlan[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfig, setShowConfig] = useState(false);
  const [configuration, setConfiguration] =
    useState<LessonPlanConfiguration | null>(null);
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [parsingError, setParsingError] = useState<string | null>(null);
  const [showAIMetrics, setShowAIMetrics] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleUpload = (content: string) => {
    setUploadedContent(content);
    setShowConfig(true);
    setParsingError(null);
  };

  const handleParsedDataReady = (result: any) => {
    if (result.success && result.data) {
      setParsedData(result.data);
      setUploadedContent(JSON.stringify(result.data)); // Set content from parsed data
      setShowConfig(true);
      setParsingError(null);
    } else {
      // Handle parsing failures but still allow manual configuration
      if (result.warnings && result.warnings.length > 0) {
        // Parsing failed but user can still proceed manually
        setParsedData(result.data);
        setUploadedContent(JSON.stringify(result.data || {}));
        setShowConfig(true);
        setParsingError(null);
        toast({
          title: "Parsing incomplete",
          description:
            "Some data could not be extracted automatically. You can still configure lesson plans manually.",
          variant: "default",
        });
      } else {
        // Complete parsing failure
        setParsingError(
          result.errors?.[0] || "An unknown error occurred during parsing."
        );
        setShowConfig(false);
      }
    }
  };

  const handleConfigurationComplete = (config: LessonPlanConfiguration) => {
    setConfiguration(config);
    setShowConfig(false);
    convertToLessonPlans(uploadedContent, config, parsedData);
  };

  const convertToLessonPlans = (
    content: string,
    config: LessonPlanConfiguration,
    parsed?: ParsedSchemeData | null
  ) => {
    setIsLoading(true);

    setTimeout(() => {
      const mockLessonPlans: LessonPlan[] = [];

      if (parsed && parsed.weeks.length > 0) {
        parsed.weeks.forEach((week, index) => {
          const coreCompetencies = getCoreCompetencies(config.learningArea);

          const lessonPlan: LessonPlan = {
            id: index + 1,
            school: config.school,
            level: config.level,
            learningArea: config.learningArea,
            date: new Date(
              config.date.getTime() + (week.week - 1) * 7 * 24 * 60 * 60 * 1000
            )
              .toISOString()
              .split("T")[0],
            time: `${8 + (week.lesson - 1) * 1}:00 AM - ${
              8 + week.lesson * 1
            }:00 AM`,
            roll: config.roll,
            term: parsed.term?.toString() || "1",
            week: week.week,
            lessonNumber: week.lesson,
            title: `${week.strand}: ${week.subStrand}`,
            strand: week.strand,
            subStrand: week.subStrand,
            specificLearningOutcomes: [
              `By the end of the lesson, the learner should be able to ${week.lessonLearningOutcome.toLowerCase()}`,
            ],
            coreCompetencies: coreCompetencies.slice(0, 3),
            keyInquiryQuestion:
              week.keyInquiryQuestion ||
              `How can we apply knowledge of ${week.subStrand} in real life situations?`,
            learningResources: week.learningResources
              ? week.learningResources.split(",").map((r) => r.trim())
              : [
                  "Textbooks and reference materials",
                  "Learning aids and manipulatives",
                  "Digital learning resources",
                ],
            introduction: {
              duration: "5 minutes",
              activities: [
                "Greet learners and take attendance",
                "Review previous lesson concepts through oral questions",
                `Introduce today's topic: ${week.subStrand}`,
                "Share the learning outcomes with learners",
              ],
            },
            lessonDevelopment: {
              duration: `${config.singleLessonDuration - 10} minutes`,
              steps: [
                {
                  stepNumber: 1,
                  activity:
                    week.learningExperiences ||
                    `Interactive exploration of ${week.subStrand} concepts`,
                  duration: "15 minutes",
                },
                {
                  stepNumber: 2,
                  activity:
                    "Guided practice with teacher support and peer collaboration",
                  duration: "15 minutes",
                },
                {
                  stepNumber: 3,
                  activity:
                    "Independent application and problem-solving activities",
                  duration: "10 minutes",
                },
              ],
            },
            conclusion: {
              duration: "5 minutes",
              activities: [
                "Summarize key learning points with learners",
                "Address any questions and clarify misconceptions",
                "Connect learning to real-life applications",
                "Preview next lesson content",
              ],
            },
            extendedActivities: [
              `Research more examples of ${week.subStrand} in the community`,
              "Create a presentation or project related to the topic",
              "Practice additional exercises for reinforcement",
              "Teach a family member about what was learned",
            ],
            assessment:
              week.assessment ||
              "Observation during activities, oral questions and answers, written exercises, peer assessment",
            teacherSelfEvaluation:
              "Reflect on lesson effectiveness and learner engagement",
            reflection:
              week.reflection ||
              "Did learners achieve the learning outcomes? What needs reinforcement in the next lesson?",
          };

          mockLessonPlans.push(lessonPlan);
        });
      } else {
        const coreCompetencies = getCoreCompetencies(config.learningArea);

        const sampleLessonPlan: LessonPlan = {
          id: 1,
          school: config.school,
          level: config.level,
          learningArea: config.learningArea,
          date: config.date.toISOString().split("T")[0],
          time: "8:00 AM - 8:40 AM",
          roll: config.roll,
          term: "1",
          week: 1,
          lessonNumber: 1,
          title: "Introduction to Technology",
          strand: "BASIC TECHNOLOGY",
          subStrand: "Technology around us",
          specificLearningOutcomes: [
            "By the end of the lesson, the learner should be able to define technology and explain its importance in daily life",
            "By the end of the lesson, the learner should be able to identify various technologies used at home, school and community",
            "By the end of the lesson, the learner should be able to appreciate the role of technology in improving quality of life",
          ],
          coreCompetencies: coreCompetencies.slice(0, 4),
          keyInquiryQuestion:
            "How does technology improve our daily lives and what would life be like without it?",
          learningResources: [
            "Pictures of various technologies",
            "Real objects (phones, computers, etc.)",
            "Chart paper and markers",
            "Digital projector",
            "Learner textbooks",
          ],
          introduction: {
            duration: "5 minutes",
            activities: [
              "Greet learners and take attendance",
              "Review previous lesson briefly through questions",
              "Introduce today's topic and share learning outcomes",
              "Create interest through a technology quiz",
            ],
          },
          lessonDevelopment: {
            duration: "30 minutes",
            steps: [
              {
                stepNumber: 1,
                activity:
                  "Brainstorming session on what technology means to learners",
                duration: "10 minutes",
              },
              {
                stepNumber: 2,
                activity:
                  "Group discussion and identification of technologies found at home and school",
                duration: "10 minutes",
              },
              {
                stepNumber: 3,
                activity:
                  "Creating a technology map of their community in groups",
                duration: "10 minutes",
              },
            ],
          },
          conclusion: {
            duration: "5 minutes",
            activities: [
              "Groups present their technology maps",
              "Summarize key points learned about technology",
              "Ask learners to share one technology they find most useful and why",
              "Preview next lesson on classification of technologies",
            ],
          },
          extendedActivities: [
            "Interview family members about technologies they use daily",
            "Create a scrapbook of different technologies found in the community",
            "Research the history of one technology they find interesting",
            "Draw and label five technologies found at home",
          ],
          assessment:
            "Observation during group discussions, oral questions and answers, technology identification worksheet, peer assessment during presentations",
          teacherSelfEvaluation:
            "Reflect on learner participation and understanding",
          reflection:
            "Did learners successfully identify and categorize different technologies? What misconceptions need to be addressed?",
        };

        mockLessonPlans.push(sampleLessonPlan);
      }

      setLessonPlans(mockLessonPlans);
      setIsLoading(false);
    }, 2000);
  };

  const resetProcess = () => {
    setUploadedContent("");
    setParsedData(null);
    setLessonPlans([]);
    setShowConfig(false);
    setConfiguration(null);
    setParsingError(null);
  };

  const handleExport = async (lessonPlan: LessonPlan, format: string) => {
    try {
      const response = await fetch(`/api/export/${format}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(lessonPlan),
      });

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `lesson_plan.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Export failed:", error);
      toast({
        title: "Export Failed",
        description: "Could not generate the document.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Premium Header */}
      <header className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-slate-200/20 dark:border-slate-700/20 sticky top-0 z-50 shadow-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl shadow-lg">
                  <GraduationCap className="h-7 w-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 bg-yellow-400 rounded-full p-1">
                  <Sparkles className="h-3 w-3 text-yellow-800" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  TeachEasy Convert
                </h1>
                <p className="text-sm text-slate-600 dark:text-slate-400 font-medium">
                  AI-Powered CBC Lesson Plans
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                onClick={() => setShowAIMetrics(!showAIMetrics)}
                variant="outline"
                className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-slate-200 dark:border-slate-700 hover:bg-white dark:hover:bg-slate-800 transition-all duration-300"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                AI Metrics
              </Button>
              {(uploadedContent || lessonPlans.length > 0) && (
                <Button
                  onClick={resetProcess}
                  variant="outline"
                  className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-slate-200 dark:border-slate-700 hover:bg-white dark:hover:bg-slate-800 transition-all duration-300"
                >
                  <BookOpen className="h-4 w-4 mr-2" />
                  New Project
                </Button>
              )}
              <Button
                variant="outline"
                size="icon"
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-slate-200 dark:border-slate-700 hover:bg-white dark:hover:bg-slate-800 transition-all duration-300"
              >
                {mounted &&
                  (theme === "dark" ? (
                    <Sun className="h-5 w-5" />
                  ) : (
                    <Moon className="h-5 w-5" />
                  ))}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-6 py-12">
        {!uploadedContent && !showConfig && (
          <>
            {/* Premium Hero Section with Animation Effects */}
            <div className="relative overflow-hidden rounded-3xl backdrop-blur-xl bg-gradient-to-r from-white/60 to-blue-100/60 dark:from-slate-900/60 dark:to-blue-900/60 shadow-2xl border border-white/20 dark:border-blue-800/20 p-8 md:p-12 mb-20 animate-in slide-in-from-bottom duration-700">
              <div className="absolute top-0 left-0 w-full h-full bg-grid-pattern opacity-10 dark:opacity-5"></div>
              <div className="absolute -top-24 -right-24 w-64 h-64 bg-gradient-to-br from-blue-500/30 to-purple-600/30 blur-3xl rounded-full"></div>
              <div className="absolute -bottom-24 -left-24 w-64 h-64 bg-gradient-to-tr from-indigo-500/30 to-cyan-400/30 blur-3xl rounded-full"></div>

              <div className="relative z-10 text-center">
                <div className="inline-flex items-center bg-white/80 dark:bg-slate-800/80 backdrop-blur-md rounded-full px-6 py-2 mb-8 border border-blue-200/50 dark:border-blue-700/50 shadow-lg animate-bounce-gentle">
                  <Award className="h-5 w-5 text-blue-600 mr-2" />
                  <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Premium AI-Powered Education Tools
                  </span>
                </div>

                <h2 className="text-5xl md:text-6xl lg:text-7xl font-extrabold mb-8 leading-tight tracking-tight">
                  <span className="relative inline-block">
                    <span className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 blur-2xl opacity-20 dark:opacity-40 rounded-3xl transform scale-110"></span>
                    <span className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                      Transform Teaching
                    </span>
                  </span>
                  <br />
                  <span className="text-slate-800 dark:text-slate-100">
                    with Smart CBC Plans
                  </span>
                </h2>

                <p className="text-xl md:text-2xl text-slate-700 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed mb-10">
                  Upload your scheme of work and{" "}
                  <span className="font-medium">instantly generate</span>{" "}
                  comprehensive, CBC-compliant lesson plans with{" "}
                  <span className="font-medium text-blue-600 dark:text-blue-400">
                    AI-powered strand identification
                  </span>
                  , core competencies mapping, and assessment strategies.
                </p>

                <div className="flex flex-wrap justify-center gap-4 mb-12">
                  <div className="inline-flex items-center bg-gradient-to-r from-green-50/80 to-emerald-50/80 dark:from-green-900/30 dark:to-emerald-900/30 backdrop-blur-md rounded-full px-5 py-2.5 border border-green-200/50 dark:border-green-700/30 shadow-lg transform transition-all duration-300 hover:scale-105">
                    <div className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-full p-1.5 mr-3 shadow-inner">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-sm font-medium text-green-800 dark:text-green-300">
                      95% Accuracy
                    </span>
                  </div>

                  <div className="inline-flex items-center bg-gradient-to-r from-blue-50/80 to-cyan-50/80 dark:from-blue-900/30 dark:to-cyan-900/30 backdrop-blur-md rounded-full px-5 py-2.5 border border-blue-200/50 dark:border-blue-700/30 shadow-lg transform transition-all duration-300 hover:scale-105">
                    <div className="bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full p-1.5 mr-3 shadow-inner">
                      <TrendingUp className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                      10x Faster
                    </span>
                  </div>

                  <div className="inline-flex items-center bg-gradient-to-r from-purple-50/80 to-fuchsia-50/80 dark:from-purple-900/30 dark:to-fuchsia-900/30 backdrop-blur-md rounded-full px-5 py-2.5 border border-purple-200/50 dark:border-purple-700/30 shadow-lg transform transition-all duration-300 hover:scale-105">
                    <div className="bg-gradient-to-r from-purple-500 to-fuchsia-500 rounded-full p-1.5 mr-3 shadow-inner">
                      <Sparkles className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-sm font-medium text-purple-800 dark:text-purple-300">
                      CBC Compliant
                    </span>
                  </div>
                </div>

                {/* Call to action */}
                <div className="mt-10">
                  <Button
                    variant="glow"
                    size="xl"
                    className="group"
                    onClick={() =>
                      document
                        .getElementById("upload-section")
                        ?.scrollIntoView({ behavior: "smooth" })
                    }
                  >
                    <span>Get Started Now</span>
                    <svg
                      width="17"
                      height="16"
                      viewBox="0 0 17 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-1 group-hover:translate-x-1 transition-transform"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M8.9999 3.33325L14.6666 7.99992L8.9999 12.6666L8.9999 8.99992L2.33325 8.99992L2.33325 6.99992L8.9999 6.99992L8.9999 3.33325Z"
                        fill="currentColor"
                      />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>

            {/* Premium Features Section with Glass Cards */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <h3 className="text-3xl font-bold text-slate-800 dark:text-slate-200 mb-4">
                  <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Premium Features
                  </span>
                </h3>
                <p className="text-lg text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
                  Harness the power of advanced AI to transform your teaching
                  materials into professional, standards-compliant lesson plans
                  in seconds.
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8 relative">
                {/* Background decorative elements */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-100/10 to-purple-100/10 dark:from-blue-900/10 dark:to-purple-900/10 rounded-3xl -m-6 blur-xl"></div>

                {[
                  {
                    icon: <Zap className="h-12 w-12" />,
                    title: "Smart Strand Detection",
                    description:
                      "Advanced AI identifies CBC strands and sub-strands with 95% accuracy from any SOW format",
                    gradient: "from-amber-400 to-orange-500",
                    bgGradient:
                      "from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20",
                    delay: "0ms",
                    features: [
                      "Multi-format support",
                      "Fallback detection",
                      "Subject-specific algorithms",
                    ],
                  },
                  {
                    icon: <Users className="h-12 w-12" />,
                    title: "Competency Mapping",
                    description:
                      "Automatically generates relevant core competencies and learning outcomes based on content analysis",
                    gradient: "from-blue-400 to-cyan-500",
                    bgGradient:
                      "from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20",
                    delay: "150ms",
                    features: [
                      "CBC alignment",
                      "Smart outcome generation",
                      "Subject-specific competencies",
                    ],
                  },
                  {
                    icon: <Award className="h-12 w-12" />,
                    title: "Assessment Integration",
                    description:
                      "Complete assessment strategies with formative, summative, and extended activities included",
                    gradient: "from-purple-400 to-pink-500",
                    bgGradient:
                      "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
                    delay: "300ms",
                    features: [
                      "Formative techniques",
                      "Summative strategies",
                      "Extended activities",
                    ],
                  },
                ].map((feature, index) => (
                  <Card
                    key={index}
                    className={`group relative overflow-hidden backdrop-blur-xl bg-white/90 dark:bg-slate-900/90 border border-white/30 dark:border-slate-700/30 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 animate-in slide-in-from-bottom`}
                    style={{ animationDelay: feature.delay }}
                  >
                    {/* Inner glow and gradient */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                      <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-transparent dark:from-slate-800/80 rounded-xl"></div>
                      <div
                        className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${feature.gradient}`}
                      ></div>
                    </div>

                    {/* Card content */}
                    <CardHeader className="relative">
                      <div
                        className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${feature.gradient} text-white shadow-lg mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500`}
                      >
                        {feature.icon}
                      </div>
                      <CardTitle className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-2">
                        {feature.title}
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="relative space-y-4">
                      <p className="text-slate-600 dark:text-slate-400 leading-relaxed text-base">
                        {feature.description}
                      </p>

                      {/* Feature list */}
                      <ul className="space-y-2 pt-2">
                        {feature.features.map((item, i) => (
                          <li
                            key={i}
                            className="flex items-center text-sm text-slate-700 dark:text-slate-300"
                          >
                            <div
                              className={`mr-2 p-1 rounded-full bg-gradient-to-r ${feature.gradient}`}
                            >
                              <CheckCircle className="h-3 w-3 text-white" />
                            </div>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Premium Upload Section */}
            <div className="relative mb-20" id="upload-section">
              {/* Background elements */}
              <div className="absolute -inset-10 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-indigo-500/5 dark:from-blue-500/10 dark:via-purple-500/10 dark:to-indigo-500/10 rounded-3xl blur-3xl"></div>
              <div className="absolute -top-10 -right-10 w-40 h-40 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 rounded-full blur-2xl"></div>
              <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-gradient-to-tr from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 rounded-full blur-2xl"></div>

              <Card className="relative overflow-hidden backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border border-white/30 dark:border-slate-700/30 shadow-2xl rounded-2xl animate-in slide-in-from-bottom duration-700">
                <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500"></div>

                <CardHeader className="relative text-center pb-8">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl mb-6 mx-auto shadow-xl shadow-blue-500/20 dark:shadow-blue-500/10 transform transition-transform hover:scale-110 hover:rotate-3 duration-500">
                    <BookOpen className="h-10 w-10 text-white" />
                  </div>
                  <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 dark:from-blue-400 dark:to-indigo-400 bg-clip-text text-transparent mb-3">
                    Upload Your Scheme of Work
                  </CardTitle>
                  <p className="text-slate-600 dark:text-slate-400 text-lg max-w-2xl mx-auto">
                    Drag & drop your SOW file or browse to select. Our premium
                    AI engine supports PDF, DOC, DOCX, and more formats.
                  </p>
                </CardHeader>

                <CardContent className="relative px-6 pb-10">
                  {parsingError && (
                    <div className="bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border border-red-200/50 dark:border-red-800/30 text-red-700 dark:text-red-300 rounded-xl p-5 mb-8 shadow-md">
                      <div className="flex items-center mb-3">
                        <div className="bg-red-100 dark:bg-red-900/50 rounded-full p-1.5 mr-3 shadow-inner">
                          <CheckCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                        </div>
                        <h4 className="font-semibold text-lg">Parsing Error</h4>
                      </div>
                      <p className="ml-9 text-sm opacity-90">{parsingError}</p>
                    </div>
                  )}

                  <div className="bg-white dark:bg-slate-800/70 rounded-xl p-6 shadow-inner border border-slate-200/50 dark:border-slate-700/50">
                    <FileUpload
                      onUpload={handleUpload}
                      onParsedDataReady={handleParsedDataReady}
                    />
                  </div>

                  <div className="flex items-center justify-center gap-2 mt-6">
                    <div className="w-2 h-2 rounded-full bg-blue-500/60 animate-pulse"></div>
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      Files are processed securely with enterprise-grade
                      encryption
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <SampleData />
          </>
        )}

        {showConfig && (
          <div className="animate-in slide-in-from-right duration-500">
            <LessonPlanConfig
              onConfigurationComplete={handleConfigurationComplete}
              isVisible={showConfig}
            />
          </div>
        )}

        {lessonPlans.length > 0 && (
          <div className="space-y-8 animate-in slide-in-from-bottom duration-700">
            <div className="text-center mb-8">
              <h3 className="text-3xl font-bold text-slate-800 dark:text-slate-200 mb-2">
                Your Generated Lesson Plans
              </h3>
              <p className="text-slate-600 dark:text-slate-400">
                Review, edit, and export your CBC-compliant lesson plans
              </p>
            </div>
            {lessonPlans.map((lessonPlan, index) => (
              <div
                key={lessonPlan.id}
                className="animate-in slide-in-from-bottom duration-700"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <LessonPlanEditor
                  lessonPlan={lessonPlan}
                  onExport={(format) => handleExport(lessonPlan, format)}
                />
              </div>
            ))}
          </div>
        )}

        {/* AI Metrics Dashboard */}
        {showAIMetrics && (
          <div className="mt-12 animate-in slide-in-from-bottom duration-700">
            <AIMetricsDashboard />
          </div>
        )}
      </main>
    </div>
  );
};

export default Index;
