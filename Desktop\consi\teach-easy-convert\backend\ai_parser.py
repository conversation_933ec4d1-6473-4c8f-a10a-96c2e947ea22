"""
AI-Powered Scheme of Work Parser using Qwen3-32B
Enhanced parsing with AI understanding and accuracy
"""
import requests
import json
import logging
from typing import Dict, List, Optional
import re
from datetime import datetime, timedelta

class AISchemeParser:
    def __init__(self, api_key: str = None):
        self.api_key = api_key or "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"
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "qwen/qwen3-235b-a22b-07-25:free"  # Updated to Qwen3 235B A22B 2507
        self.logger = logging.getLogger(__name__)
        
    def parse_scheme_with_ai(self, text_content: str, filename: str = "") -> Dict:
        """
        Parse scheme of work using AI model for enhanced accuracy
        """
        try:
            # First, let AI understand and structure the content
            structured_data = self._ai_extract_structure(text_content)
            
            if not structured_data:
                self.logger.error("AI failed to extract structure")
                return {"success": False, "error": "AI parsing failed"}
            
            # Generate CBC lesson plans from the structured data
            lesson_plans = self._generate_cbc_lesson_plans(structured_data)
            
            # Extract weeks found
            weeks_found = sorted(list(set([lp.get('week', 1) for lp in lesson_plans])))
            
            return {
                "success": True,
                "message": f"Successfully parsed {len(lesson_plans)} lesson plans using AI",
                "weeks_found": weeks_found,
                "lesson_plans": lesson_plans,
                "total_weeks": len(weeks_found),
                "confidence": 0.95  # High confidence with AI parsing
            }
            
        except Exception as e:
            self.logger.error(f"AI parsing error: {str(e)}")
            return {"success": False, "error": f"AI parsing failed: {str(e)}"}
    
    def _ai_extract_structure(self, text_content: str) -> Optional[List[Dict]]:
        """
        Use AI to extract structured data from scheme of work
        """
        prompt = f"""
You are an expert CBC (Competency-Based Curriculum) education specialist. Please analyze the following scheme of work document and extract structured lesson plan information.

DOCUMENT CONTENT:
{text_content[:8000]}  # Limit to avoid token limits

TASK: Extract lesson plan information and return a JSON array with the following structure for each lesson:

```json
[
  {{
    "week": 1,
    "lessonNumber": 1,
    "strand": "Main topic area",
    "sub_strand": "Specific topic",
    "specific_learning_outcomes": ["Outcome 1", "Outcome 2"],
    "learning_experiences": ["Activity 1", "Activity 2"],
    "key_inquiry_question": "Key question for the lesson",
    "learning_resources": ["Resource 1", "Resource 2"],
    "assessment": "Assessment method",
    "pcis": ["PCI 1", "PCI 2"],
    "values": ["Value 1", "Value 2"],
    "core_competencies": ["Competency 1", "Competency 2"]
  }}
]
```

IMPORTANT GUIDELINES:
1. Look for week numbers (Week 1, Week 2, etc.) to identify different lessons
2. Identify strands (main topic areas) and sub-strands (specific topics)
3. Extract learning outcomes (what students should achieve)
4. Find learning experiences/activities
5. Identify key inquiry questions
6. List learning resources mentioned
7. Extract assessment methods
8. For missing information, provide appropriate CBC-compliant defaults
9. Ensure all fields are present in the response
10. Return ONLY valid JSON, no additional text

RESPOND WITH VALID JSON ONLY:
"""

        try:
            response = requests.post(
                url=self.base_url,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://teach-easy-convert.com",
                    "X-Title": "CBC Lesson Plan Generator",
                },
                data=json.dumps({
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are an expert CBC education specialist. Always respond with valid JSON only."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 4000,
                    "temperature": 0.1,  # Lower temperature for more consistent results
                })
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Clean and parse JSON response
                ai_response = ai_response.strip()
                if ai_response.startswith('```json'):
                    ai_response = ai_response[7:]
                if ai_response.endswith('```'):
                    ai_response = ai_response[:-3]
                
                try:
                    parsed_data = json.loads(ai_response)
                    if isinstance(parsed_data, list):
                        return parsed_data
                    else:
                        self.logger.error(f"AI returned non-list: {type(parsed_data)}")
                        return None
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON decode error: {e}")
                    self.logger.error(f"AI Response: {ai_response}")
                    return None
            else:
                self.logger.error(f"AI API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"AI request error: {str(e)}")
            return None
    
    def _generate_cbc_lesson_plans(self, structured_data: List[Dict]) -> List[Dict]:
        """
        Generate full CBC lesson plans from structured data
        """
        lesson_plans = []
        
        for lesson_data in structured_data:
            # Generate comprehensive CBC lesson plan
            lesson_plan = {
                # Basic Information
                "school": "CBC School",
                "level": "Grade 9",
                "learning_area": "Pre-Technical Studies",
                "date": self._calculate_lesson_date(lesson_data.get('week', 1)),
                "time": "40 minutes",
                "roll": "30 students",
                
                # Lesson Details
                "week": lesson_data.get('week', 1),
                "lessonNumber": lesson_data.get('lessonNumber', 1),
                "title": f"Week {lesson_data.get('week', 1)}: {lesson_data.get('sub_strand', 'Topic')}",
                "strand": lesson_data.get('strand', 'Pre-Technical Studies'),
                "sub_strand": lesson_data.get('sub_strand', 'General Topic'),
                
                # Learning Components
                "specific_learning_outcomes": lesson_data.get('specific_learning_outcomes', [
                    "By the end of the lesson, learners should be able to demonstrate understanding of the topic"
                ]),
                "core_competencies": lesson_data.get('core_competencies', [
                    "Critical thinking and problem solving",
                    "Communication and collaboration",
                    "Creativity and imagination"
                ]),
                "pcis": lesson_data.get('pcis', [
                    "Life skills",
                    "Environmental awareness",
                    "Health education"
                ]),
                "values": lesson_data.get('values', [
                    "Responsibility",
                    "Respect",
                    "Unity"
                ]),
                
                # Lesson Structure
                "key_inquiry_question": lesson_data.get('key_inquiry_question', 
                    f"How can we apply {lesson_data.get('sub_strand', 'this topic')} in our daily lives?"),
                "learning_experiences": lesson_data.get('learning_experiences', [
                    "Interactive discussion and exploration",
                    "Hands-on activities and demonstrations",
                    "Group work and collaboration"
                ]),
                "learning_resources": lesson_data.get('learning_resources', [
                    "Textbooks and reference materials",
                    "Visual aids and charts",
                    "Practical materials and tools"
                ]),
                
                # Assessment and Reflection
                "assessment": lesson_data.get('assessment', 
                    "Observation during activities, oral questions, practical demonstrations"),
                "reflection": "What worked well? What needs improvement? How can we enhance learning?",
                
                # Lesson Plan Structure (for detailed view)
                "lesson_structure": {
                    "introduction": {
                        "duration": "5 minutes",
                        "activities": [
                            "Greet learners and take attendance",
                            "Review previous lesson",
                            f"Introduce today's topic: {lesson_data.get('sub_strand', 'Topic')}",
                            "Share learning outcomes"
                        ]
                    },
                    "development": {
                        "duration": "30 minutes",
                        "steps": [
                            {
                                "step": 1,
                                "activity": lesson_data.get('learning_experiences', ['Interactive activities'])[0],
                                "duration": "10 minutes"
                            },
                            {
                                "step": 2,
                                "activity": "Guided practice and demonstration",
                                "duration": "10 minutes"
                            },
                            {
                                "step": 3,
                                "activity": "Independent practice and application",
                                "duration": "10 minutes"
                            }
                        ]
                    },
                    "conclusion": {
                        "duration": "5 minutes",
                        "activities": [
                            "Summarize key learning points",
                            "Address questions and clarify concepts",
                            "Connect to real-life applications",
                            "Preview next lesson"
                        ]
                    }
                }
            }
            
            lesson_plans.append(lesson_plan)
        
        return lesson_plans
    
    def _calculate_lesson_date(self, week: int) -> str:
        """Calculate lesson date based on week number"""
        # Assume term starts on January 6, 2025
        start_date = datetime(2025, 1, 6)
        lesson_date = start_date + timedelta(weeks=week-1)
        return lesson_date.strftime('%Y-%m-%d')
    
    def enhance_existing_parsing(self, text_content: str, existing_result: Dict) -> Dict:
        """
        Enhance existing parsing results with AI insights
        """
        if not existing_result.get('lesson_plans'):
            return self.parse_scheme_with_ai(text_content)
        
        # Use AI to enhance existing lesson plans
        enhanced_plans = []
        
        for lesson_plan in existing_result['lesson_plans']:
            enhanced_plan = self._ai_enhance_lesson_plan(lesson_plan, text_content)
            enhanced_plans.append(enhanced_plan)
        
        return {
            **existing_result,
            "lesson_plans": enhanced_plans,
            "message": f"Enhanced {len(enhanced_plans)} lesson plans with AI insights",
            "confidence": 0.92
        }
    
    def _ai_enhance_lesson_plan(self, lesson_plan: Dict, context: str) -> Dict:
        """
        Use AI to enhance a single lesson plan with better content
        """
        prompt = f"""
Enhance this CBC lesson plan with more detailed and accurate content based on the context provided.

LESSON PLAN TO ENHANCE:
{json.dumps(lesson_plan, indent=2)}

CONTEXT FROM ORIGINAL DOCUMENT:
{context[:2000]}

TASK: Return an enhanced version of the lesson plan with:
1. More specific and actionable learning outcomes
2. Better learning experiences/activities
3. More relevant inquiry questions
4. Appropriate assessment methods
5. Proper CBC-compliant structure

Return ONLY valid JSON:
"""

        try:
            response = requests.post(
                url=self.base_url,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://teach-easy-convert.com",
                    "X-Title": "CBC Lesson Plan Generator",
                },
                data=json.dumps({
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are an expert CBC education specialist. Always respond with valid JSON only."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 2000,
                    "temperature": 0.2,
                })
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Clean and parse JSON response
                ai_response = ai_response.strip()
                if ai_response.startswith('```json'):
                    ai_response = ai_response[7:]
                if ai_response.endswith('```'):
                    ai_response = ai_response[:-3]
                
                try:
                    enhanced_plan = json.loads(ai_response)
                    return enhanced_plan
                except json.JSONDecodeError:
                    self.logger.error("Failed to parse AI enhancement")
                    return lesson_plan
            else:
                self.logger.error(f"AI enhancement API error: {response.status_code}")
                return lesson_plan
                
        except Exception as e:
            self.logger.error(f"AI enhancement error: {str(e)}")
            return lesson_plan
