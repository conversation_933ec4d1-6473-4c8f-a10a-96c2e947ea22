"use client";

import { useState, useEffect, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { EditorState, ContentState } from 'draft-js';
import { Button } from './ui/button';
import { useToast } from './ui/use-toast';

// Dynamically import the Editor with no SSR
const Editor = dynamic(
  () => import('react-draft-wysiwyg').then((mod) => mod.Editor),
  {
    ssr: false,
    loading: () => (
      <div className="border rounded-md p-4 min-h-[500px] flex items-center justify-center bg-gray-50">
        <div className="text-gray-500">Loading editor...</div>
      </div>
    ),
  }
);

interface LessonPlanEditorProps {
  lessonPlan: any;
  onExport: (format: 'pdf' | 'word') => void;
}

export function LessonPlanEditor({ lessonPlan, onExport }: LessonPlanEditorProps) {
  const [editorState, setEditorState] = useState(() => 
    typeof window !== 'undefined' ? EditorState.createEmpty() : null
  );
  const { toast } = useToast();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    if (typeof window !== 'undefined') {
      setEditorState(EditorState.createEmpty());
    }
  }, []);

  useEffect(() => {
    if (lessonPlan && isClient && editorState) {
      const content = convertLessonPlanToText(lessonPlan);
      setEditorState(EditorState.createWithContent(
        ContentState.createFromText(content)
      ));
    }
  }, [lessonPlan, isClient]);

  const convertLessonPlanToText = (plan: any): string => {
    let text = `${plan.title || 'Lesson Plan'}\n\n`;
    
    // Header information in CBC format
    text += `SCHOOL: ${plan.school || 'CBC School'}\n`;
    text += `LEVEL: ${plan.level || 'Grade 8'}\n`;
    text += `LEARNING AREA: ${plan.learning_area || plan.learningArea || 'Pre-Technical Studies'}\n`;
    text += `DATE: ${plan.date || new Date().toISOString().split('T')[0]}\n`;
    text += `TIME: ${plan.time || '40 minutes'}\n`;
    text += `ROLL: ${plan.roll || '30 students'}\n\n`;
    
    // Strand and Sub-strand
    text += `Strand: ${plan.strand || 'Pre-Technical Studies'}\n`;
    text += `Sub Strand: ${plan.sub_strand || 'General Topic'}\n\n`;
    
    // Specific Learning Outcomes
    text += `Specific Learning Outcomes:\n`;
    text += `By the end of the lesson, the learner should be able to:\n`;
    if (plan.specific_learning_outcomes?.length || plan.specificLearningOutcomes?.length) {
      const outcomes = plan.specific_learning_outcomes || plan.specificLearningOutcomes || [];
      outcomes.forEach((outcome: string, index: number) => {
        text += `${index + 1}. ${outcome}\n`;
      });
    } else {
      text += `1. Demonstrate understanding of the topic.\n`;
    }
    text += '\n';
    
    // Core Competencies
    if (plan.core_competencies?.length) {
      text += `Core Competencies:\n`;
      plan.core_competencies.forEach((comp: string) => {
        text += `• ${comp}\n`;
      });
      text += '\n';
    }
    
    // Values
    if (plan.values?.length) {
      text += `Values:\n`;
      plan.values.forEach((value: string) => {
        text += `• ${value}\n`;
      });
      text += '\n';
    }
    
    // Key Inquiry Question
    if (plan.key_inquiry_question) {
      text += `Key Inquiry Question:\n`;
      text += `${plan.key_inquiry_question}\n\n`;
    }
    
    // Learning Experiences
    if (plan.learning_experiences?.length || plan.lesson_structure) {
      text += `Learning Experiences:\n\n`;
      
      if (plan.lesson_structure) {
        // Use structured lesson format
        const structure = plan.lesson_structure;
        
        if (structure.introduction) {
          text += `Introduction (${structure.introduction.duration}):\n`;
          structure.introduction.activities.forEach((activity: string) => {
            text += `• ${activity}\n`;
          });
          text += '\n';
        }
        
        if (structure.development) {
          text += `Development (${structure.development.duration}):\n`;
          structure.development.activities.forEach((activity: string) => {
            text += `• ${activity}\n`;
          });
          text += '\n';
        }
        
        if (structure.conclusion) {
          text += `Conclusion (${structure.conclusion.duration}):\n`;
          structure.conclusion.activities.forEach((activity: string) => {
            text += `• ${activity}\n`;
          });
          text += '\n';
        }
        
        if (structure.assignment) {
          text += `Assignment (${structure.assignment.duration}):\n`;
          structure.assignment.activities.forEach((activity: string) => {
            text += `• ${activity}\n`;
          });
          text += '\n';
        }
      } else if (plan.learning_experiences?.length) {
        plan.learning_experiences.forEach((exp: string) => {
          text += `• ${exp}\n`;
        });
        text += '\n';
      }
    }
    
    // Learning Resources
    if (plan.learning_resources?.length) {
      text += `Learning Resources:\n`;
      plan.learning_resources.forEach((resource: string) => {
        text += `• ${resource}\n`;
      });
      text += '\n';
    }
    
    // Assessment
    if (plan.assessment) {
      text += `Assessment:\n`;
      if (typeof plan.assessment === 'object') {
        if (plan.assessment.formative) {
          text += `Formative: ${plan.assessment.formative}\n`;
        }
        if (plan.assessment.summative) {
          text += `Summative: ${plan.assessment.summative}\n`;
        }
        if (plan.assessment.method) {
          text += `Method: ${plan.assessment.method}\n`;
        }
      } else {
        text += `${plan.assessment}\n`;
      }
      text += '\n';
    }
    
    // Reflection
    if (plan.reflection) {
      text += `Reflection:\n`;
      if (typeof plan.reflection === 'object') {
        if (plan.reflection.teacher_reflection) {
          text += `Teacher: ${plan.reflection.teacher_reflection}\n`;
        }
        if (plan.reflection.learner_reflection) {
          text += `Learner: ${plan.reflection.learner_reflection}\n`;
        }
      } else {
        text += `${plan.reflection}\n`;
      }
    }
    
    return text;
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <Button onClick={() => onExport('pdf')}>Export as PDF</Button>
        <Button variant="outline" onClick={() => onExport('word')}>
          Export as Word
        </Button>
      </div>
      
      {isClient && editorState ? (
        <div className="border rounded-md">
          <Editor
            editorState={editorState}
            onEditorStateChange={setEditorState}
            toolbarClassName="border-b border-gray-200"
            wrapperClassName="border-t border-gray-200"
            editorClassName="p-4 min-h-[500px]"
          />
        </div>
      ) : (
        <div className="border rounded-md p-4 min-h-[500px] flex items-center justify-center bg-gray-50">
          <div className="text-gray-500">Loading editor...</div>
        </div>
      )}
    </div>
  );
}