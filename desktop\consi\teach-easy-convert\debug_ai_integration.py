"""
Debug AI Integration - Test Qwen 3 MoE Model
"""

import asyncio
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from optimized_ai_processor import OptimizedAIProcessor

async def test_qwen_integration():
    """Test if Qwen 3 MoE model is working correctly"""
    
    print("🔧 Debugging AI Integration")
    print("=" * 50)
    
    # Initialize processor
    processor = OptimizedAIProcessor()
    
    print(f"🤖 Primary Model: {processor.fast_models[0]}")
    print(f"🔗 API URL: {processor.base_url}")
    print(f"🔑 API Key: {processor.api_key[:20]}...")
    print()
    
    # Test simple content
    test_content = """
    SCIENCE TECHNOLOGY SCHEME OF WORK
    GRADE 5 TERM 2
    
    WEEK 1
    Lesson 1: Introduction to Technology
    Strand: Technology and Society
    Sub-strand: Basic Technology Concepts
    
    WEEK 2  
    Lesson 2: Simple Machines
    Strand: Technology and Society
    Sub-strand: Mechanical Systems
    """
    
    print("📝 Testing with sample content...")
    print(f"Content length: {len(test_content)} characters")
    print()
    
    try:
        # Test AI processing
        print("🚀 Testing AI processing...")
        result = await processor.process_scheme_fast(test_content, "test_scheme.txt")
        
        print("✅ AI Processing Result:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Message: {result.get('message', 'No message')}")
        print(f"   Lesson Plans: {len(result.get('lesson_plans', []))}")
        print(f"   Weeks Found: {result.get('weeks_found', [])}")
        
        if result.get('error'):
            print(f"❌ Error: {result['error']}")
            
        if result.get('lesson_plans'):
            print("\n📚 Sample Lesson Plan:")
            sample = result['lesson_plans'][0]
            print(f"   Week: {sample.get('week', 'N/A')}")
            print(f"   Title: {sample.get('title', 'N/A')}")
            print(f"   Strand: {sample.get('strand', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Exception during processing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        await processor.close()

async def test_direct_api_call():
    """Test direct API call to Qwen 3"""
    
    print("\n🔍 Testing Direct API Call")
    print("=" * 50)
    
    import aiohttp
    import json
    
    api_key = "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"
    base_url = "https://openrouter.ai/api/v1/chat/completions"
    model = "qwen/qwen3-235b-a22b:free"
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "Hello! Can you confirm you are Qwen 3 with MoE architecture?"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.1
    }
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://cbc-lesson-generator.com",
        "X-Title": "CBC Lesson Plan Generator"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"🌐 Making request to: {base_url}")
            print(f"🤖 Model: {model}")
            
            async with session.post(base_url, json=payload, headers=headers) as response:
                print(f"📡 Response Status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content']
                        print(f"✅ Response: {content}")
                    else:
                        print("❌ No choices in response")
                        print(f"Raw response: {result}")
                else:
                    error_text = await response.text()
                    print(f"❌ API Error: {error_text}")
                    
    except Exception as e:
        print(f"❌ Direct API call failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 AI Integration Debug Tool")
    print("Testing Qwen 3 MoE integration...")
    print()
    
    # Run tests
    asyncio.run(test_qwen_integration())
    asyncio.run(test_direct_api_call())
    
    print("\n✅ Debug completed!")
    print("Check the results above to identify any issues.")
