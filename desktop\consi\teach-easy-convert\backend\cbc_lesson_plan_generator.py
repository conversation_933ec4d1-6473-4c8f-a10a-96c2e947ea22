"""
CBC Lesson Plan Generator
Converts schemes of work into proper CBC-compliant lesson plans
"""
import re
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import logging

class CBCLessonPlanGenerator:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def generate_lesson_plan_from_scheme_row(self, scheme_data: Dict, config: Dict) -> Dict:
        """Generate a proper CBC lesson plan from a scheme of work row"""
        
        # Extract basic information
        week = scheme_data.get('week', 1)
        lesson_number = scheme_data.get('lessonNumber', 1)
        strand = scheme_data.get('strand', 'Pre-Technical Studies')
        sub_strand = scheme_data.get('sub_strand', 'General Topic')
        learning_outcomes = scheme_data.get('specific_learning_outcomes', [])
        learning_experiences = scheme_data.get('learning_experiences', [])
        key_inquiry_question = scheme_data.get('key_inquiry_question', '')
        learning_resources = scheme_data.get('learning_resources', [])
        assessment = scheme_data.get('assessment', '')
        
        # Calculate lesson date based on week
        start_date = datetime.strptime(config.get('start_date', '2025-01-06'), '%Y-%m-%d')
        lesson_date = start_date + timedelta(weeks=week-1, days=(lesson_number-1))
        
        # Generate proper CBC lesson plan
        lesson_plan = {
            # Header Information
            'school': config.get('school', 'CBC School'),
            'level': config.get('level', 'Grade 8'),
            'learning_area': config.get('learning_area', 'Pre-Technical Studies'),
            'date': lesson_date.strftime('%Y-%m-%d'),
            'time': config.get('time', '40 minutes'),
            'roll': config.get('roll', '30 students'),
            
            # Lesson Details
            'week': week,
            'lessonNumber': lesson_number,
            'title': f"Week {week} Lesson {lesson_number}: {sub_strand}",
            'strand': strand,
            'sub_strand': sub_strand,
            
            # Learning Components
            'specific_learning_outcomes': self._format_learning_outcomes(learning_outcomes),
            'core_competencies': self._generate_core_competencies(sub_strand),
            'pcis': self._generate_pcis(),
            'values': self._generate_values(),
            
            # Lesson Structure
            'key_inquiry_question': self._format_inquiry_question(key_inquiry_question, sub_strand),
            'learning_experiences': self._format_learning_experiences(learning_experiences, sub_strand),
            'learning_resources': self._format_learning_resources(learning_resources),
            
            # Assessment and Reflection
            'assessment': self._format_assessment(assessment),
            'reflection': self._format_reflection(),
            
            # Lesson Plan Structure
            'lesson_structure': self._generate_lesson_structure(learning_experiences, sub_strand),
        }
        
        return lesson_plan
    
    def _format_learning_outcomes(self, outcomes: List[str]) -> List[str]:
        """Format learning outcomes properly"""
        if not outcomes:
            return ["By the end of the lesson, the learner should be able to demonstrate understanding of the topic."]
        
        formatted_outcomes = []
        for outcome in outcomes:
            # Clean and format each outcome
            clean_outcome = re.sub(r'^[a-e]\)\s*', '', outcome.strip())
            clean_outcome = re.sub(r'^by\s+the\s+end.*?able\s+to[:\s]*', '', clean_outcome, flags=re.IGNORECASE)
            
            if clean_outcome and len(clean_outcome) > 10:
                # Ensure proper capitalization
                clean_outcome = clean_outcome[0].upper() + clean_outcome[1:] if clean_outcome else ''
                # Ensure it ends with a period
                if not clean_outcome.endswith('.'):
                    clean_outcome += '.'
                formatted_outcomes.append(clean_outcome)
        
        return formatted_outcomes if formatted_outcomes else ["Demonstrate understanding of the topic."]
    
    def _generate_core_competencies(self, sub_strand: str) -> List[str]:
        """Generate appropriate core competencies based on sub-strand"""
        base_competencies = [
            "Critical thinking and problem solving",
            "Creativity and imagination", 
            "Communication and collaboration"
        ]
        
        # Add specific competencies based on sub-strand
        if 'material' in sub_strand.lower():
            base_competencies.append("Learning to learn")
        elif 'tool' in sub_strand.lower():
            base_competencies.append("Self-efficacy")
        elif 'design' in sub_strand.lower():
            base_competencies.append("Creativity and imagination")
        elif 'safety' in sub_strand.lower():
            base_competencies.append("Citizenship")
        
        return base_competencies[:4]  # Max 4 competencies
    
    def _generate_pcis(self) -> List[str]:
        """Generate Pertinent and Contemporary Issues"""
        return [
            "Education for Sustainable Development (ESD)",
            "Life skills education",
            "Safety and security education"
        ]
    
    def _generate_values(self) -> List[str]:
        """Generate values to be emphasized"""
        return [
            "Responsibility",
            "Respect", 
            "Unity",
            "Peace"
        ]
    
    def _format_inquiry_question(self, question: str, sub_strand: str) -> str:
        """Format or generate key inquiry question"""
        if question and len(question) > 10:
            # Clean existing question
            clean_question = question.strip()
            if not clean_question.endswith('?'):
                clean_question += '?'
            return clean_question.capitalize()
        
        # Generate question based on sub-strand
        if 'material' in sub_strand.lower():
            return "How can we identify and use different materials effectively?"
        elif 'tool' in sub_strand.lower():
            return "Which tools are most suitable for different tasks?"
        elif 'design' in sub_strand.lower():
            return "How can we create effective designs for our projects?"
        elif 'safety' in sub_strand.lower():
            return "How can we ensure safety in our working environment?"
        else:
            return f"How can we apply {sub_strand.lower()} in our daily lives?"
    
    def _format_learning_experiences(self, experiences: List[str], sub_strand: str) -> List[str]:
        """Format learning experiences or generate appropriate ones"""
        if experiences and len(experiences) > 0:
            formatted = []
            for exp in experiences:
                clean_exp = exp.strip()
                if len(clean_exp) > 10:
                    formatted.append(clean_exp)
            if formatted:
                return formatted
        
        # Generate appropriate learning experiences
        if 'material' in sub_strand.lower():
            return [
                "Observe and examine different types of materials",
                "Identify properties of various materials through hands-on exploration",
                "Classify materials based on their characteristics",
                "Discuss uses of different materials in daily life"
            ]
        elif 'tool' in sub_strand.lower():
            return [
                "Identify different types of tools and their uses",
                "Demonstrate proper handling of tools",
                "Practice using tools safely under supervision",
                "Discuss the importance of tool maintenance"
            ]
        elif 'design' in sub_strand.lower():
            return [
                "Analyze existing designs and their features",
                "Create simple sketches and drawings",
                "Develop design ideas through brainstorming",
                "Present and discuss design concepts"
            ]
        else:
            return [
                "Engage in guided exploration and discovery",
                "Participate in group discussions and activities",
                "Practice skills through hands-on activities",
                "Reflect on learning and share experiences"
            ]
    
    def _format_learning_resources(self, resources: List[str]) -> List[str]:
        """Format learning resources or provide defaults"""
        if resources and len(resources) > 0:
            return [r.strip() for r in resources if r.strip()]
        
        return [
            "Pre-Technical Studies textbook",
            "Real materials and specimens",
            "Charts and diagrams",
            "Digital devices and internet",
            "Workshop tools (for demonstration)",
            "Exercise books and drawing materials"
        ]
    
    def _format_assessment(self, assessment: str) -> Dict[str, str]:
        """Format assessment methods"""
        if assessment and len(assessment) > 10:
            clean_assessment = assessment.strip()
        else:
            clean_assessment = "Observation, oral questions, practical demonstration"
        
        return {
            "formative": "Observation during activities, oral questioning, peer discussion",
            "summative": clean_assessment,
            "method": "Continuous assessment through observation and practical tasks"
        }
    
    def _format_reflection(self) -> Dict[str, str]:
        """Generate reflection questions"""
        return {
            "learner_reflection": "What new things did I learn today? How can I apply this knowledge?",
            "teacher_reflection": "Were the learning outcomes achieved? What teaching strategies worked best? What can be improved in future lessons?"
        }
    
    def _generate_lesson_structure(self, experiences: List[str], sub_strand: str) -> Dict[str, Dict]:
        """Generate detailed lesson structure with timing"""
        return {
            "introduction": {
                "duration": "5 minutes",
                "activities": [
                    "Welcome and roll call",
                    "Recap of previous lesson",
                    "Introduction of today's topic",
                    "Sharing of learning objectives"
                ]
            },
            "development": {
                "duration": "25 minutes", 
                "activities": self._format_learning_experiences(experiences, sub_strand)
            },
            "conclusion": {
                "duration": "8 minutes",
                "activities": [
                    "Summary of key learning points",
                    "Questions and clarifications",
                    "Assessment of learning outcomes",
                    "Preview of next lesson"
                ]
            },
            "assignment": {
                "duration": "2 minutes",
                "activities": [
                    "Give homework or take-home activity",
                    "Provide additional resources for further learning"
                ]
            }
        }
