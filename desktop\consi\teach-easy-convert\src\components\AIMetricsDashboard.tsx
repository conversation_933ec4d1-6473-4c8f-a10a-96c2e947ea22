import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  Target,
  TrendingUp,
  CheckCircle2,
  Cpu,
  Zap,
  BarChart3,
  Shield,
} from "lucide-react";

interface AIMetrics {
  overall_success_rate: number;
  ai_ensemble_success_rate: number;
  validation_pass_rate: number;
  enhancement_success_rate: number;
  total_documents_processed: number;
  current_accuracy_threshold: number;
  current_confidence_threshold: number;
  models_deployed: string[];
  processing_pipeline: string;
}

interface AIMetricsDashboardProps {
  metrics?: AIMetrics;
  className?: string;
}

export const AIMetricsDashboard: React.FC<AIMetricsDashboardProps> = ({
  metrics,
  className = "",
}) => {
  const [liveMetrics, setLiveMetrics] = useState<AIMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchLiveMetrics = async () => {
    setIsLoading(true);
    try {
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";
      const response = await fetch(`${API_BASE_URL}/ai-metrics/`);
      if (response.ok) {
        const data = await response.json();
        setLiveMetrics(data);
      }
    } catch (error) {
      console.error("Failed to fetch AI metrics:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLiveMetrics();
  }, []);

  const displayMetrics = metrics || liveMetrics;

  if (!displayMetrics) {
    return (
      <Card className={`bg-slate-900/50 border-slate-700 ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-slate-200">
            <Brain className="h-5 w-5 text-blue-400" />
            <span>AI Performance Dashboard</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-slate-400">Loading AI metrics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const MetricCard = ({
    icon: Icon,
    title,
    value,
    suffix = "%",
    color = "blue",
    description,
  }: {
    icon: any;
    title: string;
    value: number;
    suffix?: string;
    color?: string;
    description?: string;
  }) => (
    <div className="bg-slate-800/50 p-4 rounded-lg border border-slate-700">
      <div className="flex items-center space-x-2 mb-2">
        <Icon className={`h-4 w-4 text-${color}-400`} />
        <span className="text-sm font-medium text-slate-200">{title}</span>
      </div>
      <div className="space-y-2">
        <div className="text-2xl font-bold text-white">
          {value.toFixed(1)}
          {suffix}
        </div>
        {description && <p className="text-xs text-slate-400">{description}</p>}
        <Progress
          value={suffix === "%" ? value : (value / 100) * 100}
          className="h-2"
        />
      </div>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Metrics Overview */}
      <Card className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-500/30">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-blue-100">
            <Brain className="h-6 w-6 text-blue-400" />
            <span>🚀 AI-Powered CBC Generator - Performance Dashboard</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCard
              icon={Target}
              title="Overall Success Rate"
              value={displayMetrics.overall_success_rate}
              color="green"
              description="Documents successfully parsed"
            />

            <MetricCard
              icon={Zap}
              title="AI Ensemble Success"
              value={displayMetrics.ai_ensemble_success_rate}
              color="blue"
              description="Multi-model AI processing"
            />

            <MetricCard
              icon={Shield}
              title="Validation Pass Rate"
              value={displayMetrics.validation_pass_rate}
              color="purple"
              description="Quality assurance checks"
            />

            <MetricCard
              icon={TrendingUp}
              title="Enhancement Success"
              value={displayMetrics.enhancement_success_rate}
              color="orange"
              description="AI content improvements"
            />
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Processing Stats */}
        <Card className="bg-slate-900/50 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-slate-200">
              <BarChart3 className="h-5 w-5 text-green-400" />
              <span>Processing Statistics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Total Documents Processed</span>
              <Badge
                variant="outline"
                className="bg-green-500/20 text-green-300 border-green-500/50"
              >
                {displayMetrics.total_documents_processed}
              </Badge>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-slate-400">Accuracy Threshold</span>
              <Badge
                variant="outline"
                className="bg-blue-500/20 text-blue-300 border-blue-500/50"
              >
                {displayMetrics.current_accuracy_threshold}%
              </Badge>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-slate-400">Confidence Threshold</span>
              <Badge
                variant="outline"
                className="bg-purple-500/20 text-purple-300 border-purple-500/50"
              >
                {displayMetrics.current_confidence_threshold}%
              </Badge>
            </div>

            <div className="pt-2 border-t border-slate-700">
              <span className="text-slate-400 text-sm">
                Processing Pipeline
              </span>
              <p className="text-green-300 font-medium mt-1">
                {displayMetrics.processing_pipeline}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* AI Models */}
        <Card className="bg-slate-900/50 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-slate-200">
              <Cpu className="h-5 w-5 text-orange-400" />
              <span>AI Models Deployed</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {displayMetrics.models_deployed.map((model, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg border border-slate-700"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-slate-200 font-medium">
                      {model.replace("/", " / ").replace(":", " ")}
                    </span>
                  </div>
                  <Badge
                    variant="outline"
                    className="bg-green-500/20 text-green-300 border-green-500/50 text-xs"
                  >
                    Active
                  </Badge>
                </div>
              ))}
            </div>

            <div className="mt-4 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle2 className="h-4 w-4 text-blue-400" />
                <span className="text-blue-300 text-sm font-medium">
                  Multi-Model Ensemble
                </span>
              </div>
              <p className="text-xs text-blue-200">
                Advanced AI orchestration with validation, enhancement, and
                quality assurance
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Refresh Button */}
      <div className="flex justify-center">
        <button
          onClick={fetchLiveMetrics}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white rounded-lg transition-colors flex items-center space-x-2"
        >
          {isLoading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <TrendingUp className="h-4 w-4" />
          )}
          <span>{isLoading ? "Refreshing..." : "Refresh Metrics"}</span>
        </button>
      </div>
    </div>
  );
};

export default AIMetricsDashboard;
