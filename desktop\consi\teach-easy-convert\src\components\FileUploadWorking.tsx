"use client";

import React, { useState, useRef } from "react";
import dynamic from "next/dynamic";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Upload, FileText, ClipboardPaste, Zap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { ParsingResult } from "@/utils/schemeParser";
import AIProcessingIndicator from "./AIProcessingIndicator";
import AIStatusIndicator from "./AIStatusIndicator";

// Dynamically import File icon to disable SSR
const FileIcon = dynamic(() => import("lucide-react").then((mod) => mod.File), {
  ssr: false,
});

interface FileUploadProps {
  onUpload: (content: string) => void;
  onParsedDataReady?: (result: any) => void;
}

export default function FileUpload({
  onUpload,
  onParsedDataReady,
}: FileUploadProps) {
  const [textContent, setTextContent] = useState("");
  const [dragOver, setDragOver] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [aiProcessingResult, setAiProcessingResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const acceptedFormats = [
    ".pdf",
    ".doc",
    ".docx",
    ".xls",
    ".xlsx",
    ".txt",
    ".rtf",
    ".odt",
  ];

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const parseFile = async (file: File) => {
    setIsProcessing(true);
    const formData = new FormData();
    formData.append("file", file);

    const API_BASE_URL =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

    try {
      console.log(`Making request to: ${API_BASE_URL}/parse-scheme/`);

      const response = await fetch(`${API_BASE_URL}/parse-scheme/`, {
        method: "POST",
        body: formData,
      });

      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`Server error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log("Parsed data:", data);

      // Set AI processing result for indicator
      setAiProcessingResult(data);

      if (onParsedDataReady) {
        // Convert new backend format to old frontend format for compatibility
        const result = {
          success: data.success,
          data: data.success
            ? {
                title: data.lesson_plans[0]?.school || "Parsed Scheme of Work",
                grade: data.lesson_plans[0]?.level || "Grade 9",
                learningArea:
                  data.lesson_plans[0]?.learning_area || "Not detected",
                term: data.lesson_plans[0]?.term || 1,
                weeks: (data.lesson_plans || []).map((lp: any) => ({
                  week: lp.week,
                  lesson: lp.lessonNumber || 1,
                  strand: lp.strand,
                  subStrand: lp.sub_strand,
                  lessonLearningOutcome: Array.isArray(
                    lp.specific_learning_outcomes
                  )
                    ? lp.specific_learning_outcomes.join(". ")
                    : lp.specific_learning_outcomes || "To be determined",
                  learningExperiences: Array.isArray(lp.activities)
                    ? lp.activities.join(". ")
                    : lp.activities || "Interactive learning activities",
                  keyInquiryQuestion:
                    lp.key_inquiry_question ||
                    "How can we apply this learning?",
                  learningResources: Array.isArray(lp.learning_resources)
                    ? lp.learning_resources.join(", ")
                    : lp.learning_resources || "Standard learning materials",
                  assessment: lp.assessment || "Observation and questioning",
                  reflection:
                    lp.reflection || "Lesson effectiveness evaluation",
                })),
              }
            : null,
          errors: data.success ? [] : [data.message],
          warnings: [],
        };
        onParsedDataReady(result);
      }

      if (data.success) {
        toast({
          title: "Scheme parsed successfully!",
          description: `${data.message} Found ${data.lesson_plans.length} lessons. Please review the extracted information.`,
        });
      } else {
        toast({
          title: "Parsing encountered issues",
          description: data.message || "Could not parse the scheme format.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("File upload and parsing error:", error);

      let errorMessage = "An unexpected error occurred during file processing.";
      if (
        error.message.includes("fetch") ||
        error.message.includes("Failed to fetch")
      ) {
        errorMessage = `Cannot connect to backend server. Make sure the backend is running on ${API_BASE_URL}.`;
      } else if (error.message.includes("Server error")) {
        errorMessage = error.message;
      } else {
        errorMessage = error.message || "Unknown error occurred.";
      }

      toast({
        title: "Error processing file",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFileName(file.name);
      parseFile(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    if (e.dataTransfer.files?.length) {
      const file = e.dataTransfer.files[0];
      setUploadedFileName(file.name);
      parseFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleTextSubmit = async () => {
    if (!textContent.trim()) {
      toast({
        title: "Text content is empty",
        description: "Please enter your scheme of work content.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    const API_BASE_URL =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

    try {
      const response = await fetch(`${API_BASE_URL}/parse-text/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ text_content: textContent }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`Server error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log("Parsed data:", data);

      // Set AI processing result for indicator
      setAiProcessingResult(data);

      onUpload(textContent);

      if (onParsedDataReady) {
        // Convert new backend format to old frontend format for compatibility
        const result = {
          success: data.success,
          data: data.success
            ? {
                title: "Parsed Scheme of Work (Text Input)",
                grade: data.lesson_plans[0]?.level || "Grade 9",
                learningArea:
                  data.lesson_plans[0]?.learning_area || "Not detected",
                term: data.lesson_plans[0]?.term || 1,
                weeks: (data.lesson_plans || []).map((lp: any) => ({
                  week: lp.week,
                  lesson: lp.lessonNumber || 1,
                  strand: lp.strand,
                  subStrand: lp.sub_strand,
                  lessonLearningOutcome: Array.isArray(
                    lp.specific_learning_outcomes
                  )
                    ? lp.specific_learning_outcomes.join(". ")
                    : lp.specific_learning_outcomes || "To be determined",
                  learningExperiences: Array.isArray(lp.activities)
                    ? lp.activities.join(". ")
                    : lp.activities || "Interactive learning activities",
                  keyInquiryQuestion:
                    lp.key_inquiry_question ||
                    "How can we apply this learning?",
                  learningResources: Array.isArray(lp.learning_resources)
                    ? lp.learning_resources.join(", ")
                    : lp.learning_resources || "Standard learning materials",
                  assessment: lp.assessment || "Observation and questioning",
                  reflection:
                    lp.reflection || "Lesson effectiveness evaluation",
                })),
              }
            : null,
          errors: data.success ? [] : [data.message],
          warnings: [],
        };
        onParsedDataReady(result);
      }

      if (data.success) {
        toast({
          title: "Scheme parsed successfully!",
          description: `${data.message} Found ${data.lesson_plans.length} lessons from text.`,
        });
      } else {
        toast({
          title: "Parsing encountered issues",
          description: data.message || "Could not parse the text content.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Text parsing error:", error);

      let errorMessage = "An unexpected error occurred during text processing.";
      if (
        error.message.includes("fetch") ||
        error.message.includes("Failed to fetch")
      ) {
        errorMessage = `Cannot connect to backend server. Make sure the backend is running on ${API_BASE_URL}.`;
      } else if (error.message.includes("Server error")) {
        errorMessage = error.message;
      } else {
        errorMessage = error.message || "Unknown error occurred.";
      }

      toast({
        title: "Error processing text",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Transform Your Teaching with AI
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Upload your CBC Scheme of Work and generate professional lesson plans
          instantly using advanced AI technology
        </p>
        <div className="flex items-center justify-center gap-2 mt-4">
          <AIStatusIndicator />
        </div>
      </div>

      {/* Upload Section */}
      <Card className="border-2 border-dashed border-gray-300 hover:border-blue-500 transition-colors">
        <CardContent className="p-8">
          <div
            className={`text-center space-y-4 ${
              dragOver ? "bg-blue-50 border-blue-300" : ""
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <div className="flex justify-center">
              <Upload className="h-12 w-12 text-gray-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                🤖 AI-Powered Scheme Analysis
              </h3>
              <p className="text-gray-500">
                Our advanced AI understands your content and generates accurate
                CBC lesson plans
              </p>
              <p className="text-sm text-gray-400 mt-2">
                Supports: {acceptedFormats.join(", ")} • Enhanced with Qwen3-32B
                AI
              </p>
            </div>
            <Button onClick={handleFileSelect} className="mx-auto" size="lg">
              <FileText className="mr-2 h-4 w-4" />
              Choose File
            </Button>
            {uploadedFileName && !isProcessing && (
              <p className="text-sm text-green-600">
                Uploaded: {uploadedFileName}
              </p>
            )}
            {isProcessing && (
              <div className="flex items-center space-x-2 text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <p className="text-sm">🤖 AI analyzing your scheme...</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Text Input Alternative */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <ClipboardPaste className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-medium">
                Or paste your content for AI analysis
              </h3>
            </div>
            <Textarea
              placeholder="Paste your scheme of work content here... AI will analyze and generate CBC lesson plans automatically"
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
              rows={8}
              className="min-h-[200px]"
            />
            <Button
              onClick={handleTextSubmit}
              disabled={!textContent.trim() || isProcessing}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  🤖 AI Processing...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Generate with AI
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(",")}
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* AI Processing Indicator */}
      {(isProcessing || aiProcessingResult) && (
        <AIProcessingIndicator
          isProcessing={isProcessing}
          result={aiProcessingResult}
        />
      )}
    </div>
  );
}
