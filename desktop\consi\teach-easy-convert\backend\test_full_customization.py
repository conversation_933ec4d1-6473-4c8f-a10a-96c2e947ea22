#!/usr/bin/env python3
"""
Test Full Teacher Customization System
Demonstrates enterprise-level lesson plan generation with complete teacher customization
"""

import asyncio
import sys
import json
sys.path.append('.')

from enterprise_ai_processor import EnterpriseAIProcessor, ProcessingProgress
from optimized_file_processor import OptimizedFileProcessor
from teacher_customization import <PERSON>Customizer, TeacherProfile, TEACHER_PRESETS

def progress_callback(progress: ProcessingProgress):
    """Handle progress updates"""
    percentage = (progress.completed_weeks / progress.total_weeks) * 100 if progress.total_weeks > 0 else 0
    print(f"📊 {percentage:.1f}% | Week {progress.current_week}/{progress.total_weeks} | Lessons: {progress.completed_lessons} | {progress.status}")

async def test_full_customization():
    print("🎓 FULL TEACHER CUSTOMIZATION SYSTEM TEST")
    print("=" * 70)
    
    # Test different teacher profiles
    test_profiles = {
        "Mrs. Wanjiku - Urban Public School": TeacherProfile(
            teacher_name="Mrs. <PERSON>",
            school_name="Nairobi Primary School",
            school_type="Public",
            county="Nairobi",
            grade_level="Grade 5",
            subject="Science Technology",
            term="Term 2",
            class_size=42,
            teaching_style="Interactive",
            has_projector=False,
            has_computers=False,
            has_internet=False,
            include_local_examples=True,
            include_career_connections=True,
            preferred_activities=["Group discussions", "Demonstrations", "Practical activities"]
        ),
        
        "Mr. Kipchoge - Rural School": TeacherProfile(
            teacher_name="Mr. Daniel Kipchoge",
            school_name="Eldoret Hills Primary School",
            school_type="Public",
            county="Eldoret",
            grade_level="Grade 5",
            subject="Science Technology",
            term="Term 2",
            class_size=28,
            teaching_style="Practical",
            has_projector=False,
            has_computers=False,
            has_internet=False,
            outdoor_space=True,
            include_local_examples=True,
            difficulty_level="Standard",
            preferred_activities=["Field work", "Hands-on experiments", "Local examples"]
        ),
        
        "Ms. Akinyi - Private School": TeacherProfile(
            teacher_name="Ms. Sarah Akinyi",
            school_name="Kisumu International Academy",
            school_type="Private",
            county="Kisumu",
            grade_level="Grade 5",
            subject="Science Technology",
            term="Term 2",
            class_size=20,
            teaching_style="Inquiry-based",
            has_projector=True,
            has_computers=True,
            has_internet=True,
            has_science_lab=True,
            difficulty_level="Advanced",
            include_cross_curricular=True,
            preferred_activities=["Research projects", "Technology integration", "Problem solving"]
        )
    }
    
    # Initialize processors
    file_processor = OptimizedFileProcessor()
    enterprise_processor = EnterpriseAIProcessor()
    customizer = TeacherCustomizer()
    
    # Extract PDF content once
    print("📄 EXTRACTING STM2025.PDF CONTENT...")
    print("-" * 50)
    
    with open('../STM2025.pdf', 'rb') as f:
        file_content = f.read()
    
    content, metadata = file_processor.extract_text_fast(file_content, 'STM2025.pdf', 'customization_test')
    print(f"✅ Extracted {len(content):,} characters from {metadata.get('pages_processed', 0)} pages")
    print()
    
    # Test each teacher profile
    for profile_name, teacher_profile in test_profiles.items():
        print(f"👩‍🏫 TESTING: {profile_name}")
        print("=" * 70)
        
        print(f"🏫 School: {teacher_profile.school_name} ({teacher_profile.school_type})")
        print(f"📍 Location: {teacher_profile.county}")
        print(f"👥 Class Size: {teacher_profile.class_size} students")
        print(f"🎯 Teaching Style: {teacher_profile.teaching_style}")
        print(f"💻 Technology: Projector={teacher_profile.has_projector}, Computers={teacher_profile.has_computers}, Internet={teacher_profile.has_internet}")
        print(f"🔬 Lab Access: {teacher_profile.has_science_lab}")
        print()
        
        # Set progress callback
        enterprise_processor.set_progress_callback(progress_callback)
        
        # Convert teacher profile to dict for processing
        teacher_prefs = {
            'school_name': teacher_profile.school_name,
            'teacher_name': teacher_profile.teacher_name,
            'grade_level': teacher_profile.grade_level,
            'subject': teacher_profile.subject,
            'term': teacher_profile.term,
            'class_size': teacher_profile.class_size,
            'teaching_style': teacher_profile.teaching_style,
            'county': teacher_profile.county
        }
        
        print("🤖 Processing with Enterprise AI...")
        result = await enterprise_processor.process_large_scheme(content, 'STM2025.pdf', teacher_prefs)
        
        if result['success']:
            lessons = result['lesson_plans']
            print(f"✅ Generated {len(lessons)} base lessons")
            
            # Apply full teacher customization
            print("🎨 Applying teacher customization...")
            customized_lessons = []
            
            for lesson in lessons[:3]:  # Customize first 3 lessons for demo
                customized_lesson = customizer.customize_lesson_plan(lesson, teacher_profile)
                customized_lessons.append(customized_lesson)
            
            print(f"✅ Customized {len(customized_lessons)} lessons")
            print()
            
            # Show customized lesson example
            if customized_lessons:
                lesson = customized_lessons[0]
                print("📋 SAMPLE CUSTOMIZED LESSON PLAN:")
                print("-" * 50)
                print(f"🏫 School: {lesson.get('school', 'N/A')}")
                print(f"👩‍🏫 Teacher: {lesson.get('teacher', 'N/A')}")
                print(f"📊 Level: {lesson.get('level', 'N/A')} | Class Size: {lesson.get('class_size', 'N/A')}")
                print(f"⏰ Duration: {lesson.get('duration', 'N/A')}")
                print(f"🗣️ Language: {lesson.get('language', 'N/A')}")
                print(f"📖 Strand: {lesson.get('strand', 'N/A')}")
                print(f"🎯 Sub-strand: {lesson.get('sub_strand', 'N/A')}")
                
                # Learning experiences
                experiences = lesson.get('learning_experiences', [])
                if experiences:
                    print(f"🎭 Learning Experiences:")
                    for exp in experiences[:3]:
                        print(f"   • {exp}")
                
                # Resources
                resources = lesson.get('learning_resources', [])
                if resources:
                    print(f"📦 Resources:")
                    for res in resources[:4]:
                        print(f"   • {res}")
                
                # Assessment
                assessment = lesson.get('assessment', 'N/A')
                additional_assessments = lesson.get('additional_assessments', [])
                print(f"📝 Assessment: {assessment}")
                if additional_assessments:
                    print(f"📝 Additional: {', '.join(additional_assessments[:2])}")
                
                # Local examples
                local_examples = lesson.get('local_examples', [])
                if local_examples:
                    print(f"🌍 Local Examples: {', '.join(local_examples)}")
                
                # Career connections
                career_connections = lesson.get('career_connections', [])
                if career_connections:
                    print(f"💼 Career Connections: {', '.join(career_connections)}")
                
                # Cross-curricular
                cross_curricular = lesson.get('cross_curricular', {})
                if cross_curricular:
                    print(f"🔗 Cross-curricular:")
                    for subject, connections in cross_curricular.items():
                        print(f"   {subject}: {', '.join(connections[:2])}")
                
                # Special accommodations
                accommodations = lesson.get('special_needs_accommodations', [])
                if accommodations:
                    print(f"♿ Special Needs: {', '.join(accommodations[:2])}")
                
                print()
            
            # Save customized lessons for this teacher
            filename = f"customized_lessons_{teacher_profile.teacher_name.replace(' ', '_').replace('.', '')}.json"
            with open(filename, 'w') as f:
                json.dump(customized_lessons, f, indent=2)
            print(f"💾 Saved customized lessons to: {filename}")
            
        else:
            print(f"❌ Processing failed: {result['message']}")
        
        print("\n" + "="*70 + "\n")
        
        # Brief pause between profiles
        await asyncio.sleep(2)
    
    # Cleanup
    await enterprise_processor.close()
    
    print("🎉 FULL CUSTOMIZATION TEST COMPLETE!")
    print("✅ Successfully demonstrated enterprise-level teacher customization")
    print("📊 Each teacher now has personalized lesson plans for their specific context")

if __name__ == "__main__":
    print("🚀 Starting Full Teacher Customization Test...")
    print("💡 This demonstrates how the system adapts to different teacher needs!")
    print()
    
    asyncio.run(test_full_customization())
