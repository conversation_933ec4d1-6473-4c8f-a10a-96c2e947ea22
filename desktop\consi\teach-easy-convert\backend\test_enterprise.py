#!/usr/bin/env python3
"""
Test Enterprise AI Processor with Full STM2025.pdf
Demonstrates handling of large scheme documents with 15+ weeks
"""

import asyncio
import sys
import time
sys.path.append('.')

from enterprise_ai_processor import EnterpriseAIProcessor, ProcessingProgress
from optimized_file_processor import OptimizedFileProcessor

def progress_callback(progress: ProcessingProgress):
    """Handle progress updates"""
    percentage = (progress.completed_weeks / progress.total_weeks) * 100 if progress.total_weeks > 0 else 0
    print(f"📊 Progress: {percentage:.1f}% | Week {progress.current_week}/{progress.total_weeks} | Lessons: {progress.completed_lessons} | ETA: {progress.estimated_time_remaining:.0f}s | {progress.status}")

async def test_enterprise_processing():
    print("🏢 ENTERPRISE CBC LESSON PLAN GENERATOR TEST")
    print("=" * 60)
    
    # Teacher customization preferences
    teacher_preferences = {
        'school_name': 'Mombasa Primary School',
        'grade_level': 'Grade 5',
        'subject': 'Science Technology', 
        'term': 'Term 2',
        'teacher_name': 'Mrs. <PERSON>',
        'class_size': 35,
        'teaching_style': 'Interactive and practical',
        'assessment_preference': 'Continuous assessment with practical activities'
    }
    
    print(f"👩‍🏫 Teacher: {teacher_preferences['teacher_name']}")
    print(f"🏫 School: {teacher_preferences['school_name']}")
    print(f"📚 Subject: {teacher_preferences['subject']} - {teacher_preferences['grade_level']} - {teacher_preferences['term']}")
    print(f"👥 Class Size: {teacher_preferences['class_size']} students")
    print()
    
    # Initialize processors
    file_processor = OptimizedFileProcessor()
    enterprise_processor = EnterpriseAIProcessor()
    enterprise_processor.set_progress_callback(progress_callback)
    
    try:
        # Step 1: Extract PDF content
        print("📄 STEP 1: EXTRACTING PDF CONTENT")
        print("-" * 40)
        
        with open('../STM2025.pdf', 'rb') as f:
            file_content = f.read()
        
        content, metadata = file_processor.extract_text_fast(file_content, 'STM2025.pdf', 'enterprise_test')
        
        print(f"✅ PDF Extraction Complete:")
        print(f"   📊 Pages: {metadata.get('pages_processed', 0)}/{metadata.get('total_pages', 0)}")
        print(f"   📝 Content: {len(content):,} characters")
        print(f"   ⚡ Method: {metadata.get('extraction_method', 'Unknown')}")
        print(f"   📖 Preview: {content[:200]}...")
        print()
        
        # Step 2: Enterprise AI Processing
        print("🤖 STEP 2: ENTERPRISE AI PROCESSING")
        print("-" * 40)
        print("🔄 Starting chunked processing with DeepSeek V3 (Free)...")
        print("⏱️ This may take 2-5 minutes for a full term scheme...")
        print()
        
        start_time = time.time()
        result = await enterprise_processor.process_large_scheme(
            content, 
            'STM2025.pdf', 
            teacher_preferences
        )
        processing_time = time.time() - start_time
        
        # Step 3: Results Analysis
        print("\n" + "=" * 60)
        print("📊 PROCESSING RESULTS")
        print("=" * 60)
        
        if result['success']:
            lessons = result['lesson_plans']
            
            print(f"✅ SUCCESS: {result['message']}")
            print(f"🔧 Method: {result.get('processing_method', 'Unknown')}")
            print(f"⏱️ Time: {processing_time:.1f} seconds")
            print(f"📈 Rate: {result.get('lessons_per_second', 0):.2f} lessons/second")
            print(f"📊 Statistics:")
            print(f"   📅 Weeks processed: {result.get('weeks_processed', 0)}")
            print(f"   📚 Total lessons: {len(lessons)}")
            print(f"   ❌ Failed weeks: {result.get('failed_weeks', 0)}")
            print()
            
            # Analyze lesson distribution
            week_distribution = {}
            subjects_found = set()
            strands_found = set()
            
            for lesson in lessons:
                week = lesson.get('week', 'Unknown')
                week_distribution[week] = week_distribution.get(week, 0) + 1
                subjects_found.add(lesson.get('learning_area', 'Unknown'))
                strands_found.add(lesson.get('strand', 'Unknown'))
            
            print("📈 LESSON DISTRIBUTION:")
            for week in sorted(week_distribution.keys()):
                print(f"   Week {week}: {week_distribution[week]} lessons")
            print()
            
            print(f"🎯 SUBJECTS COVERED: {', '.join(subjects_found)}")
            print(f"📖 STRANDS FOUND: {len(strands_found)} unique strands")
            print()
            
            # Show sample lessons
            print("📋 SAMPLE LESSON PLANS:")
            print("-" * 40)
            
            for i, lesson in enumerate(lessons[:3]):  # Show first 3 lessons
                print(f"📚 Lesson {i+1}:")
                print(f"   🗓️ Week: {lesson.get('week', 'N/A')}")
                print(f"   🏫 School: {lesson.get('school', 'N/A')}")
                print(f"   📊 Level: {lesson.get('level', 'N/A')}")
                print(f"   📖 Strand: {lesson.get('strand', 'N/A')}")
                print(f"   🎯 Sub-strand: {lesson.get('sub_strand', 'N/A')}")
                
                outcomes = lesson.get('specific_learning_outcomes', [])
                if outcomes:
                    print(f"   🎓 Learning Outcomes:")
                    for outcome in outcomes[:2]:  # Show first 2 outcomes
                        print(f"      • {outcome}")
                
                resources = lesson.get('learning_resources', [])
                if resources:
                    print(f"   📦 Resources: {', '.join(resources[:3])}")
                
                print()
            
            if len(lessons) > 3:
                print(f"   ... and {len(lessons) - 3} more lessons")
            
            print("\n🎉 ENTERPRISE PROCESSING SUCCESSFUL!")
            print(f"Your {teacher_preferences['school_name']} now has {len(lessons)} customized lesson plans!")
            
        else:
            print(f"❌ PROCESSING FAILED: {result['message']}")
            if 'error' in result:
                print(f"💥 Error: {result['error']}")
        
        # Cleanup
        await enterprise_processor.close()
        
    except Exception as e:
        print(f"💥 CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        
        # Ensure cleanup
        try:
            await enterprise_processor.close()
        except:
            pass

if __name__ == "__main__":
    print("🚀 Starting Enterprise CBC Lesson Plan Generator...")
    print("💡 This system can handle 15+ weeks with 120+ lessons!")
    print()
    
    asyncio.run(test_enterprise_processing())
