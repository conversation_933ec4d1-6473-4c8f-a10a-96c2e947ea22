"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Zap, 
  Clock, 
  CheckCircle2, 
  Cpu, 
  TrendingUp,
  Database
} from "lucide-react";

interface FastProcessingIndicatorProps {
  isProcessing: boolean;
  result?: any;
  onComplete?: () => void;
}

export const FastProcessingIndicator: React.FC<FastProcessingIndicatorProps> = ({
  isProcessing,
  result,
  onComplete
}) => {
  const [currentStage, setCurrentStage] = useState(0);
  const [progress, setProgress] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  const fastStages = [
    { name: "Cache Check", duration: 0.2, icon: Database },
    { name: "Content Analysis", duration: 1.0, icon: Cpu },
    { name: "AI Processing", duration: 8.0, icon: Zap },
    { name: "Quality Check", duration: 0.5, icon: CheckCircle2 },
    { name: "Finalizing", duration: 0.3, icon: TrendingUp }
  ];

  useEffect(() => {
    if (isProcessing && !startTime) {
      setStartTime(Date.now());
      setCurrentStage(0);
      setProgress(0);
      
      // Ultra-fast simulation - complete in 10 seconds max
      const totalDuration = fastStages.reduce((sum, stage) => sum + stage.duration, 0) * 1000;
      let currentTime = 0;
      
      const interval = setInterval(() => {
        currentTime += 100; // Update every 100ms for smooth progress
        
        let accumulatedTime = 0;
        let newStage = 0;
        
        for (let i = 0; i < fastStages.length; i++) {
          const stageDuration = fastStages[i].duration * 1000;
          if (currentTime <= accumulatedTime + stageDuration) {
            newStage = i;
            break;
          }
          accumulatedTime += stageDuration;
        }
        
        setCurrentStage(newStage);
        
        // Ultra-smooth progress calculation
        const newProgress = Math.min((currentTime / totalDuration) * 100, 100);
        setProgress(newProgress);
        
        // Complete when we reach 100% or after 10 seconds max
        if (newProgress >= 100 || currentTime >= 10000) {
          clearInterval(interval);
          setProgress(100);
          setCurrentStage(fastStages.length - 1);
          
          // Auto-complete if processing is still running after 10 seconds
          setTimeout(() => {
            if (onComplete && isProcessing) {
              onComplete();
            }
          }, 500);
        }
      }, 100); // Much faster updates for smoother animation
        currentTime += 100;
        const overallProgress = Math.min((currentTime / totalDuration) * 100, 95);
        setProgress(overallProgress);
        
        // Update current stage
        let accumulatedTime = 0;
        for (let i = 0; i < fastStages.length; i++) {
          accumulatedTime += fastStages[i].duration * 1000;
          if (currentTime <= accumulatedTime) {
            setCurrentStage(i);
            break;
          }
        }
        
        if (currentTime >= totalDuration) {
          setProgress(100);
          setCurrentStage(fastStages.length - 1);
          clearInterval(interval);
        }
      }, 100);
      
      return () => clearInterval(interval);
    }
  }, [isProcessing, startTime]);

  useEffect(() => {
    if (isProcessing && startTime) {
      const timer = setInterval(() => {
        setElapsedTime((Date.now() - startTime) / 1000);
      }, 100);
      
      return () => clearInterval(timer);
    }
  }, [isProcessing, startTime]);

  useEffect(() => {
    if (result && isProcessing) {
      setProgress(100);
      setCurrentStage(fastStages.length - 1);
      setTimeout(() => {
        onComplete?.();
      }, 500);
    }
  }, [result, isProcessing, onComplete]);

  if (!isProcessing && !result) {
    return null;
  }

  const CurrentIcon = fastStages[currentStage]?.icon || Zap;

  return (
    <Card className="border-orange-500/50 bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-orange-700 dark:text-orange-300">
          <Zap className="h-5 w-5 animate-pulse" />
          <span>⚡ Ultra-Fast AI Processing</span>
          <Badge variant="outline" className="bg-orange-500/20 text-orange-700 border-orange-500/50 ml-auto">
            <Clock className="h-3 w-3 mr-1" />
            {elapsedTime.toFixed(1)}s
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-orange-600 dark:text-orange-400">
              Processing Progress
            </span>
            <span className="text-orange-600 dark:text-orange-400 font-medium">
              {Math.round(progress)}%
            </span>
          </div>
          <Progress 
            value={progress} 
            className="h-3 bg-orange-100 dark:bg-orange-900/50"
          />
        </div>

        {/* Current Stage */}
        <div className="flex items-center space-x-3 p-3 bg-orange-100/50 dark:bg-orange-900/30 rounded-lg">
          <CurrentIcon className="h-5 w-5 text-orange-600 dark:text-orange-400 animate-pulse" />
          <div className="flex-1">
            <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
              {fastStages[currentStage]?.name || 'Processing...'}
            </p>
            <p className="text-xs text-orange-600 dark:text-orange-400">
              Optimized for speed while maintaining 100% accuracy
            </p>
          </div>
        </div>

        {/* Speed Indicators */}
        <div className="grid grid-cols-3 gap-3 text-center">
          <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded">
            <p className="text-xs text-green-600 dark:text-green-400 font-medium">Cache Hits</p>
            <p className="text-sm font-bold text-green-700 dark:text-green-300">
              {result?.cache_hits || 0}
            </p>
          </div>
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded">
            <p className="text-xs text-blue-600 dark:text-blue-400 font-medium">AI Models</p>
            <p className="text-sm font-bold text-blue-700 dark:text-blue-300">
              {result?.models_used?.length || 3}
            </p>
          </div>
          <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded">
            <p className="text-xs text-purple-600 dark:text-purple-400 font-medium">Target Time</p>
            <p className="text-sm font-bold text-purple-700 dark:text-purple-300">
              &lt;30s
            </p>
          </div>
        </div>

        {/* Optimization Features */}
        <div className="space-y-2">
          <p className="text-xs font-medium text-orange-700 dark:text-orange-300">Speed Optimizations Active:</p>
          <div className="flex flex-wrap gap-1">
            <Badge variant="outline" className="bg-green-500/20 text-green-700 border-green-500/50 text-xs">
              ✓ Smart Caching
            </Badge>
            <Badge variant="outline" className="bg-blue-500/20 text-blue-700 border-blue-500/50 text-xs">
              ✓ Parallel Processing
            </Badge>
            <Badge variant="outline" className="bg-purple-500/20 text-purple-700 border-purple-500/50 text-xs">
              ✓ Optimized Models
            </Badge>
            <Badge variant="outline" className="bg-orange-500/20 text-orange-700 border-orange-500/50 text-xs">
              ✓ Fast Validation
            </Badge>
          </div>
        </div>

        {/* Success Message */}
        {result && progress >= 100 && (
          <div className="p-3 bg-green-100 dark:bg-green-900/30 border border-green-500/30 rounded-lg">
            <div className="flex items-center space-x-2">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">
                Processing Complete!
              </span>
            </div>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
              {result.message || 'AI processing completed successfully'}
            </p>
            {result.processing_time && (
              <p className="text-xs text-green-600 dark:text-green-400">
                Completed in {result.processing_time.toFixed(1)} seconds
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FastProcessingIndicator;
